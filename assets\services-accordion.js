// Services Horizontal Accordion with GSAP
document.addEventListener("DOMContentLoaded", () => {
  const accordionPanels = document.querySelectorAll(".accordion-panel");

  if (accordionPanels.length === 0) return;

  // Initialize accordion - no panel active initially
  let activePanel = null;

  // GSAP timeline for smooth animations
  const tl = gsap.timeline({ paused: true });

  // Function to activate a panel
  function activatePanel(panel) {
    if (panel === activePanel) return;

    // Create a timeline for smooth sequential animations
    const tl = gsap.timeline();

    // If there's an active panel, reset it first with timeline
    if (activePanel) {
      // Reset previous panel with timeline
      tl.to(activePanel.querySelector(".panel-details"), {
        opacity: 0,
        y: 15,
        duration: 0.25,
        ease: "power2.inOut",
        onComplete: () => {
          activePanel.querySelector(".panel-details").style.visibility =
            "hidden";
        },
      })
        .to(
          activePanel.querySelector(".panel-background img"),
          {
            scale: 1,
            duration: 0.4,
            ease: "power2.inOut",
          },
          "<"
        )
        .to(
          activePanel.querySelector(".panel-title"),
          {
            scale: 1,
            duration: 0.3,
            ease: "power2.inOut",
          },
          "<"
        );
    }

    // Remove active class from all panels
    accordionPanels.forEach((p) => p.classList.remove("active"));

    // Add active class to clicked panel
    panel.classList.add("active");

    // Update active panel reference
    activePanel = panel;

    // Animate new panel activation with timeline
    tl.fromTo(
      panel.querySelector(".panel-details"),
      {
        opacity: 0,
        y: 20,
        visibility: "hidden",
      },
      {
        opacity: 1,
        y: 0,
        visibility: "visible",
        duration: 0.6,
        ease: "power2.out",
      },
      "+=0.1" // Small delay after reset
    )
      .fromTo(
        panel.querySelector(".panel-title"),
        {
          scale: 0.95,
        },
        {
          scale: 1,
          duration: 0.5,
          ease: "back.out(1.2)",
        },
        "<+0.1"
      )
      .fromTo(
        panel.querySelector(".panel-background img"),
        {
          scale: 1.05,
        },
        {
          scale: 1,
          duration: 0.6,
          ease: "power2.out",
        },
        "<"
      );
  }

  // Add click event listeners to panels
  accordionPanels.forEach((panel) => {
    panel.addEventListener("click", (e) => {
      // Prevent link clicks from triggering panel activation
      if (e.target.classList.contains("panel-link")) {
        return;
      }

      activatePanel(panel);
    });

    // Add hover effects
    panel.addEventListener("mouseenter", () => {
      if (panel !== activePanel) {
        gsap.to(panel.querySelector(".panel-background img"), {
          scale: 1.05,
          duration: 0.4,
          ease: "power2.out",
        });
      }
    });

    panel.addEventListener("mouseleave", () => {
      if (panel !== activePanel) {
        gsap.to(panel.querySelector(".panel-background img"), {
          scale: 1,
          duration: 0.4,
          ease: "power2.out",
        });
      }
    });
  });

  // Auto-rotate functionality (optional)
  let autoRotateInterval;
  let isAutoRotating = false;

  function startAutoRotate() {
    if (isAutoRotating) return;

    isAutoRotating = true;
    autoRotateInterval = setInterval(() => {
      let nextIndex;
      if (activePanel) {
        const currentIndex = Array.from(accordionPanels).indexOf(activePanel);
        nextIndex = (currentIndex + 1) % accordionPanels.length;
      } else {
        nextIndex = 0; // Start with first panel if none active
      }
      activatePanel(accordionPanels[nextIndex]);
    }, 4000); // Change panel every 4 seconds
  }

  function stopAutoRotate() {
    if (!isAutoRotating) return;

    isAutoRotating = false;
    clearInterval(autoRotateInterval);
  }

  // Start auto-rotate after initial load
  setTimeout(startAutoRotate, 2000);

  // Stop auto-rotate on user interaction
  accordionPanels.forEach((panel) => {
    panel.addEventListener("click", stopAutoRotate);
    panel.addEventListener("mouseenter", stopAutoRotate);
  });

  // Simple initialization - no scroll animations
  const accordionContainer = document.querySelector(
    ".services-horizontal-accordion"
  );

  // Keyboard navigation
  document.addEventListener("keydown", (e) => {
    let nextIndex;

    switch (e.key) {
      case "ArrowLeft":
        e.preventDefault();
        if (!activePanel) {
          nextIndex = accordionPanels.length - 1; // Start from last panel
        } else {
          const currentIndex = Array.from(accordionPanels).indexOf(activePanel);
          nextIndex =
            currentIndex > 0 ? currentIndex - 1 : accordionPanels.length - 1;
        }
        activatePanel(accordionPanels[nextIndex]);
        stopAutoRotate();
        break;
      case "ArrowRight":
        e.preventDefault();
        if (!activePanel) {
          nextIndex = 0; // Start from first panel
        } else {
          const currentIndex = Array.from(accordionPanels).indexOf(activePanel);
          nextIndex =
            currentIndex < accordionPanels.length - 1 ? currentIndex + 1 : 0;
        }
        activatePanel(accordionPanels[nextIndex]);
        stopAutoRotate();
        break;
    }
  });

  // Touch/swipe support for mobile
  let touchStartX = 0;
  let touchEndX = 0;

  accordionContainer.addEventListener("touchstart", (e) => {
    touchStartX = e.changedTouches[0].screenX;
  });

  accordionContainer.addEventListener("touchend", (e) => {
    touchEndX = e.changedTouches[0].screenX;
    handleSwipe();
  });

  function handleSwipe() {
    const swipeThreshold = 50;
    const diff = touchStartX - touchEndX;

    if (Math.abs(diff) > swipeThreshold) {
      let nextIndex;

      if (diff > 0) {
        // Swipe left - next panel
        if (!activePanel) {
          nextIndex = 0; // Start from first panel
        } else {
          const currentIndex = Array.from(accordionPanels).indexOf(activePanel);
          nextIndex =
            currentIndex < accordionPanels.length - 1 ? currentIndex + 1 : 0;
        }
      } else {
        // Swipe right - previous panel
        if (!activePanel) {
          nextIndex = accordionPanels.length - 1; // Start from last panel
        } else {
          const currentIndex = Array.from(accordionPanels).indexOf(activePanel);
          nextIndex =
            currentIndex > 0 ? currentIndex - 1 : accordionPanels.length - 1;
        }
      }

      activatePanel(accordionPanels[nextIndex]);
      stopAutoRotate();
    }
  }
});
