// Services Section - OH Architecture Process Style
document.addEventListener("DOMContentLoaded", () => {
  const processSteps = document.querySelectorAll(".services-process-step");
  const servicesImage = document.querySelector(".services-image img");

  if (processSteps.length === 0) return;

  // Intersection Observer for process steps
  const observerOptions = {
    threshold: 0.3,
    rootMargin: "0px 0px -50px 0px",
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("in-view");
      }
    });
  }, observerOptions);

  // Observe all process steps with staggered delays
  processSteps.forEach((step, index) => {
    // Add initial delay based on step index
    step.style.transitionDelay = `${index * 0.1}s`;
    observer.observe(step);
  });

  // Add hover effects to process steps
  processSteps.forEach((step) => {
    step.addEventListener("mouseenter", () => {
      gsap.to(step, {
        y: -5,
        duration: 0.3,
        ease: "power2.out",
      });
    });

    step.addEventListener("mouseleave", () => {
      gsap.to(step, {
        y: 0,
        duration: 0.3,
        ease: "power2.out",
      });
    });
  });

  // Parallax effect on services image
  let ticking = false;

  function updateParallax() {
    if (servicesImage) {
      const rect = servicesImage.getBoundingClientRect();
      const isVisible = rect.top < window.innerHeight && rect.bottom > 0;

      if (isVisible) {
        const scrollProgress =
          (window.innerHeight - rect.top) / (window.innerHeight + rect.height);
        const clampedProgress = Math.max(0, Math.min(1, scrollProgress));

        // Subtle parallax effect
        const translateY = (clampedProgress - 0.5) * 20;
        gsap.set(servicesImage, {
          y: translateY,
        });
      }
    }

    ticking = false;
  }

  function handleScroll() {
    if (!ticking) {
      requestAnimationFrame(updateParallax);
      ticking = true;
    }
  }

  // Add scroll listener for parallax
  window.addEventListener("scroll", handleScroll);

  // Initial check
  updateParallax();
});
