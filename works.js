// Works Page JavaScript
document.addEventListener("DOMContentLoaded", () => {
  const worksContainer = document.getElementById("works-container");
  const progressBar = document.getElementById("progress-bar");
  const projectCards = document.querySelectorAll(".project-card");

  // Check if mobile
  function isMobile() {
    return window.innerWidth <= 768;
  }

  // Calculate and set exact scroll width to prevent extra white space (desktop only)
  function setExactScrollWidth() {
    if (isMobile()) return; // Skip on mobile

    const worksScroll = document.getElementById("works-scroll");
    if (worksScroll && projectCards.length > 0) {
      // Get computed styles
      const cardWidth = projectCards[0].offsetWidth;
      const gap = 64; // 4rem = 64px
      const paddingLeft = 80; // 5rem = 80px
      const paddingRight = 80; // 5rem = 80px

      // Calculate exact width needed
      const totalWidth =
        cardWidth * projectCards.length +
        gap * (projectCards.length - 1) +
        paddingLeft +
        paddingRight;
      worksScroll.style.width = `${totalWidth}px`;
    }
  }

  // Set exact width on load and resize (desktop only)
  setExactScrollWidth();
  window.addEventListener("resize", setExactScrollWidth);

  // Scroll Progress Tracker (desktop only)
  function updateScrollProgress() {
    if (!worksContainer || !progressBar || isMobile()) return;

    const scrollLeft = worksContainer.scrollLeft;
    const scrollWidth = worksContainer.scrollWidth - worksContainer.clientWidth;
    const scrollPercentage = (scrollLeft / scrollWidth) * 100;

    progressBar.style.width = `${Math.min(scrollPercentage, 100)}%`;
  }

  // Add scroll event listener (desktop only)
  if (worksContainer && !isMobile()) {
    worksContainer.addEventListener("scroll", updateScrollProgress);

    // Prevent scrolling beyond the last card (desktop only)
    worksContainer.addEventListener("scroll", () => {
      const maxScrollLeft =
        worksContainer.scrollWidth - worksContainer.clientWidth;
      if (worksContainer.scrollLeft > maxScrollLeft) {
        worksContainer.scrollLeft = maxScrollLeft;
      }
    });
  }

  // Convert mouse wheel to horizontal scroll (desktop only)
  if (worksContainer && !isMobile()) {
    worksContainer.addEventListener("wheel", (e) => {
      e.preventDefault();
      worksContainer.scrollLeft += e.deltaY;
    });
  }

  // Project card click handlers
  projectCards.forEach((card, index) => {
    card.addEventListener("click", () => {
      const projectNumber = card.getAttribute("data-project");

      // Navigate to dynamic project page
      const projectIds = [
        "modern-villa", // 1
        "urban-loft", // 2
        "corporate-office", // 3
        "luxury-penthouse", // 4
        "sustainable-house", // 5 - Minimalist House
        "art-gallery", // 6 - Cultural Center
        "boutique-hotel", // 7 - Eco Resort
        "medical-center", // 8 - Glass Pavilion
        "coastal-retreat", // 9 - Riverside Residence
        "innovation-hub", // 10 - Innovation Hub
        "warehouse-conversion", // 11 (if needed)
        "school-extension", // 12 (if needed)
      ];
      const projectId = projectIds[parseInt(projectNumber) - 1];

      if (projectId) {
        window.location.href = `project.html?project=${projectId}`;
      } else {
        console.error("Project not found:", projectNumber);
      }
    });
  });

  // GSAP Animations
  if (typeof gsap !== "undefined") {
    // Animate Works title on load
    // gsap.fromTo(
    //   ".works-title h1",
    //   {
    //     opacity: 0,
    //     x: -100,
    //   },
    //   {
    //     opacity: 1,
    //     x: 0,
    //     duration: 1.5,
    //     ease: "power3.out",
    //     delay: 0.5,
    //   }
    // );

    // Animate progress bar on load
    gsap.fromTo(
      ".scroll-progress",
      {
        opacity: 0,
        y: -20,
      },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power2.out",
        delay: 0.3,
      }
    );

    // Parallax effect for Works title based on scroll
    if (worksContainer) {
      worksContainer.addEventListener("scroll", () => {
        const scrollLeft = worksContainer.scrollLeft;
        const maxScroll =
          worksContainer.scrollWidth - worksContainer.clientWidth;
        const scrollRatio = scrollLeft / maxScroll;

        // Move title slightly as user scrolls
        // gsap.to(".works-title h1", {
        //   x: scrollRatio * 50,
        //   duration: 0.1,
        //   ease: "none",
        // });

        // Fade title opacity based on scroll
        // const opacity = Math.max(0.1, 1 - scrollRatio * 0.8);
        // gsap.to(".works-title h1", {
        //   opacity: opacity,
        //   duration: 0.1,
        //   ease: "none",
        // });
      });
    }

    // Hover animations for project cards
    projectCards.forEach((card) => {
      const image = card.querySelector(".project-image img");

      card.addEventListener("mouseenter", () => {
        gsap.to(image, {
          scale: 1.1,
          duration: 0.5,
          ease: "power2.out",
        });

        // Keep project-info always visible - no animation
      });

      card.addEventListener("mouseleave", () => {
        gsap.to(image, {
          scale: 1,
          duration: 0.5,
          ease: "power2.out",
        });

        // Keep project-info always visible - no animation
      });
    });
  }

  // Simple keyboard navigation
  document.addEventListener("keydown", (e) => {
    if (!worksContainer) return;

    const cardScrollAmount = worksContainer.clientWidth * 0.65; // One card width approximately

    switch (e.key) {
      case "ArrowLeft":
        e.preventDefault();
        worksContainer.scrollLeft -= cardScrollAmount;
        break;
      case "ArrowRight":
        e.preventDefault();
        worksContainer.scrollLeft += cardScrollAmount;
        break;
      case "Home":
        e.preventDefault();
        worksContainer.scrollLeft = 0;
        break;
      case "End":
        e.preventDefault();
        worksContainer.scrollLeft = worksContainer.scrollWidth;
        break;
    }
  });

  // Touch/swipe support for horizontal scroll (desktop only)
  // On mobile, we want normal vertical scrolling behavior
  if (!isMobile()) {
    let startX = 0;
    let scrollStart = 0;
    let isScrolling = false;

    if (worksContainer) {
      worksContainer.addEventListener("touchstart", (e) => {
        startX = e.touches[0].clientX;
        scrollStart = worksContainer.scrollLeft;
        isScrolling = true;
      });

      worksContainer.addEventListener("touchmove", (e) => {
        if (!isScrolling) return;

        e.preventDefault();
        const currentX = e.touches[0].clientX;
        const diffX = startX - currentX;
        worksContainer.scrollLeft = scrollStart + diffX;
      });

      worksContainer.addEventListener("touchend", () => {
        isScrolling = false;
      });
    }
  }

  // Initialize scroll progress on load
  updateScrollProgress();

  // Handle navbar scroll behavior
  const navbar = document.querySelector(".new-navbar");
  if (navbar) {
    if (isMobile()) {
      // Mobile: use window scroll for vertical scrolling
      window.addEventListener("scroll", () => {
        const scrollTop =
          window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > 100) {
          navbar.classList.add("scrolled");
        } else {
          navbar.classList.remove("scrolled");
        }
      });
    } else if (worksContainer) {
      // Desktop: use horizontal scroll
      worksContainer.addEventListener("scroll", () => {
        const scrollLeft = worksContainer.scrollLeft;

        // Hide logo when scrolled (similar to vertical scroll behavior)
        if (scrollLeft > 100) {
          navbar.classList.add("scrolled");
        } else {
          navbar.classList.remove("scrolled");
        }
      });
    }
  }
});
