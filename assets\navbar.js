// Unified Navbar for both Index and About pages
document.addEventListener("DOMContentLoaded", () => {
  const navbar = document.getElementById("navbar");
  const menuButton = document.getElementById("menu-button");
  // Handle both contact button IDs (contact-button for index, mobile-contact for about)
  const contactButton =
    document.getElementById("contact-button") ||
    document.getElementById("mobile-contact");
  const menuOverlay = document.getElementById("menu-overlay");
  const menuClose = document.getElementById("menu-close");
  const menuNavLinks = document.querySelectorAll(".menu-nav a");
  const menuImages = document.querySelectorAll(".menu-image");

  let isMenuOpen = false;
  let lastScrollY = 0;

  // Check if device is mobile or tablet
  function isMobileOrTablet() {
    return window.innerWidth <= 1024; // Covers both mobile and tablet
  }

  // Initialize menu button visibility for mobile/tablet
  function initializeMenuButton() {
    if (isMobileOrTablet()) {
      // Always show menu button and contact button on mobile/tablet
      menuButton.classList.add("show");
      contactButton.classList.add("show");
      navbar.classList.add("hide");
    } else {
      // Desktop behavior - show navbar initially, hide buttons
      menuButton.classList.remove("show");
      contactButton.classList.remove("show");
      navbar.classList.remove("hide");
      // Ensure navbar is visible on desktop
      navbar.style.opacity = "";
      navbar.style.pointerEvents = "";
    }
  }

  // Scroll handler for navbar transformation
  function handleScroll() {
    const currentScrollY = window.scrollY;

    // Detect hero section height based on page type
    let heroHeight = window.innerHeight;
    let scrollThreshold = 0.8; // Default threshold

    const aboutHero = document.querySelector(".about-hero");
    const heroSection = document.querySelector(".hero-section");
    const processIntro = document.querySelector("#intro");

    if (aboutHero) {
      // About page - use about hero height
      heroHeight = aboutHero.offsetHeight;
      scrollThreshold = 0.7;
    } else if (heroSection) {
      // Index page - use hero section height
      heroHeight = heroSection.offsetHeight;
      scrollThreshold = 0.8;
    } else if (processIntro) {
      // Process page - use intro section height
      heroHeight = processIntro.offsetHeight;
      scrollThreshold = 0.6;
    }

    const scrollTriggerPoint = heroHeight * scrollThreshold;

    if (isMobileOrTablet()) {
      // Mobile/tablet behavior - always show buttons
      menuButton.classList.add("show");
      contactButton.classList.add("show");
      navbar.classList.add("hide");
    } else {
      // Desktop behavior
      if (currentScrollY > scrollTriggerPoint) {
        // Scrolled past hero - show buttons, hide navbar
        menuButton.classList.add("show");
        contactButton.classList.add("show");
        navbar.classList.add("hide");
      } else {
        // In hero section - show navbar, hide buttons
        menuButton.classList.remove("show");
        contactButton.classList.remove("show");
        navbar.classList.remove("hide");
      }
    }

    lastScrollY = currentScrollY;
  }

  // Menu functionality
  function openMenu() {
    if (isMenuOpen) return;

    isMenuOpen = true;
    menuOverlay.classList.add("active");
    document.body.style.overflow = "hidden";

    // Hide menu button and contact button when menu is open
    menuButton.style.opacity = "0";
    menuButton.style.pointerEvents = "none";
    contactButton.style.opacity = "0";
    contactButton.style.pointerEvents = "none";

    // Show first image by default
    if (menuImages.length > 0) {
      menuImages[0].classList.add("active");
    }
  }

  function closeMenu() {
    if (!isMenuOpen) return;

    isMenuOpen = false;
    menuOverlay.classList.remove("active");
    document.body.style.overflow = "";

    // Restore menu button and contact button visibility
    menuButton.style.opacity = "";
    menuButton.style.pointerEvents = "";
    contactButton.style.opacity = "";
    contactButton.style.pointerEvents = "";

    // Hide all images
    menuImages.forEach((img) => img.classList.remove("active"));
  }

  // Menu hover effects
  menuNavLinks.forEach((link, index) => {
    link.addEventListener("mouseenter", () => {
      // Hide all images
      menuImages.forEach((img) => img.classList.remove("active"));
      // Show corresponding image
      if (menuImages[index]) {
        menuImages[index].classList.add("active");
      }
    });
  });

  // Event listeners
  if (menuButton) {
    menuButton.addEventListener("click", openMenu);
  }

  if (menuClose) {
    menuClose.addEventListener("click", closeMenu);
  }

  if (menuOverlay) {
    menuOverlay.addEventListener("click", (e) => {
      if (e.target === menuOverlay) {
        closeMenu();
      }
    });
  }

  // Handle menu navigation clicks
  menuNavLinks.forEach((link) => {
    link.addEventListener("click", (e) => {
      const href = link.getAttribute("href");

      // Handle anchor links (same page navigation)
      if (href.startsWith("#")) {
        e.preventDefault();
        closeMenu();

        // Wait for menu close animation
        setTimeout(() => {
          const targetSection = document.querySelector(href);
          if (targetSection) {
            targetSection.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        }, 500);
      }
      // For regular links (like about.html), let them work normally
      else {
        closeMenu();
      }
    });
  });

  // Initialize on load
  initializeMenuButton();
  handleScroll();

  // Add scroll event listener
  window.addEventListener("scroll", handleScroll);

  // Handle window resize
  window.addEventListener("resize", () => {
    initializeMenuButton();
    handleScroll();
  });

  // Expose handleScroll globally for other scripts
  window.handleNavbarScroll = handleScroll;

  // Handle contact button clicks
  if (contactButton) {
    contactButton.addEventListener("click", () => {
      // Open contact sidebar (handled by contact-sidebar.js)
      const contactSidebar = document.getElementById("contact-sidebar");
      if (contactSidebar) {
        contactSidebar.classList.add("active");
        document.body.style.overflow = "hidden";
      }
    });
  }

  // Keyboard navigation
  document.addEventListener("keydown", (e) => {
    if (e.key === "Escape" && isMenuOpen) {
      closeMenu();
    }
  });

  // Smooth scroll for all anchor links on the page
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute("href"));
      if (target) {
        target.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
        if (isMenuOpen) {
          closeMenu();
        }
      }
    });
  });

  console.log(
    "Unified navbar initialized for",
    aboutHero ? "about" : "index",
    "page"
  );
});
