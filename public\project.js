// Project Page JavaScript - Clean and Simple
document.addEventListener("DOMContentLoaded", () => {
  console.log("Project page initialized - no animations");

  // Ensure all content is visible immediately
  const allElements = document.querySelectorAll(
    ".project-hero-content, .description-content h2, .description-content p, .project-details, .gallery-image, .cta-content, .footer"
  );

  allElements.forEach((element) => {
    element.style.opacity = "1";
    element.style.transform = "none";
  });

  // Ensure images load properly
  const images = document.querySelectorAll("img");
  images.forEach((img) => {
    img.style.opacity = "1";
    img.style.transform = "none";
  });

  // Smooth scroll for internal links
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute("href"));
      if (target) {
        target.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    });
  });

  // Simple hover effects for gallery images (minimal)
  const galleryImages = document.querySelectorAll(".gallery-image");
  galleryImages.forEach((image) => {
    const img = image.querySelector("img");
    if (img) {
      image.addEventListener("mouseenter", () => {
        img.style.transform = "scale(1.02)";
        img.style.transition = "transform 0.3s ease";
      });

      image.addEventListener("mouseleave", () => {
        img.style.transform = "scale(1)";
      });
    }
  });

  console.log("Project page initialized - clean and simple");
});

  // Smooth scroll for internal links
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute("href"));
      if (target) {
        target.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    });
  });

  // Simple image loading - no lazy loading complications
  const images = document.querySelectorAll("img");

  images.forEach((img) => {
    // Ensure images are visible and load normally
    img.style.opacity = "1";
    img.style.transform = "none";
  });

  // Add scroll progress indicator
  const progressBar = document.createElement("div");
  progressBar.className = "scroll-progress-bar";
  progressBar.innerHTML = '<div class="progress-fill"></div>';
  document.body.appendChild(progressBar);

  // Style the progress bar
  const style = document.createElement("style");
  style.textContent = `
    .scroll-progress-bar {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background: rgba(255, 255, 255, 0.2);
      z-index: 9999;
    }
    
    .progress-fill {
      height: 100%;
      background: #2a4d3b;
      width: 0%;
      transition: width 0.1s ease;
    }
  `;
  document.head.appendChild(style);

  // Update progress bar on scroll
  const progressFill = document.querySelector(".progress-fill");

  window.addEventListener("scroll", () => {
    const scrollTop = window.pageYOffset;
    const docHeight = document.body.scrollHeight - window.innerHeight;
    const scrollPercent = (scrollTop / docHeight) * 100;

    if (progressFill) {
      progressFill.style.width = scrollPercent + "%";
    }
  });

  // Simple hover effects for gallery images
  galleryImages.forEach((image) => {
    const img = image.querySelector("img");

    if (img) {
      image.addEventListener("mouseenter", () => {
        img.style.transform = "scale(1.02)";
        img.style.transition = "transform 0.3s ease";
      });

      image.addEventListener("mouseleave", () => {
        img.style.transform = "scale(1)";
      });
    }
  });

  // Simple click handlers for CTA buttons
  const ctaButtons = document.querySelectorAll(".cta-btn");

  ctaButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      // Simple click feedback
      this.style.transform = "scale(0.98)";
      setTimeout(() => {
        this.style.transform = "scale(1)";
      }, 100);
    });
  });

  // Footer Animation
  const footer = document.querySelector(".footer");
  if (footer) {
    gsap.fromTo(
      ".footer",
      {
        opacity: 0,
        y: 30,
      },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out",
        scrollTrigger: {
          trigger: ".footer",
          start: "top 85%",
          end: "bottom 15%",
          toggleActions: "play none none reverse",
        },
      }
    );
  }

  // Footer Navigation and CTA button hover animations are handled by animations.js

  console.log("Project page initialized with GSAP animations");
});
