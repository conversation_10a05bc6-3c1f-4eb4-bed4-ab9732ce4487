<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <!-- SEO Meta Tags -->
  <title>
    About Our Studio - DIQRA Architects | Award-Winning Design Team
  </title>
  <meta name="description"
    content="Learn about DIQRA Architects' design philosophy, our experienced team, and our commitment to creating innovative architectural spaces. Discover our approach to sustainable and thoughtful design." />
  <meta name="keywords"
    content="about DIQRA architects, architecture studio, design team, architectural philosophy, sustainable design, building design approach, architecture firm history" />
  <meta name="author" content="DIQRA Architects" />
  <meta name="robots" content="index, follow" />
  <link rel="canonical" href="https://diqraarchitects.com/about.html" />

  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="About Our Studio - DIQRA Architects | Award-Winning Design Team" />
  <meta property="og:description"
    content="Learn about DIQRA Architects' design philosophy and our commitment to creating innovative architectural spaces." />
  <meta property="og:image" content="https://cdn.cosmos.so/0098a074-f8a2-4821-bcb0-433c093ae255.jpeg" />
  <meta property="og:url" content="https://diqraarchitects.com/about.html" />
  <meta property="og:type" content="website" />
  <meta property="og:site_name" content="DIQRA Architects" />

  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="About Our Studio - DIQRA Architects" />
  <meta name="twitter:description"
    content="Learn about our design philosophy and commitment to innovative architecture." />
  <meta name="twitter:image" content="https://cdn.cosmos.so/0098a074-f8a2-4821-bcb0-433c093ae255.jpeg" />

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="public/favicon.ico" />
  <link rel="apple-touch-icon" sizes="180x180" href="public/apple-touch-icon.png" />

  <!-- Stylesheets -->
  <link rel="stylesheet" href="style.css" />
  <!-- import tailwind  -->
  <link href="https://unpkg.com/tailwindcss@^4.1.10/dist/tailwind.min.css" rel="stylesheet" />

  <!-- Structured Data (JSON-LD) -->
  <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "AboutPage",
        "mainEntity": {
          "@type": "ArchitecturalService",
          "name": "DIQRA Architects",
          "description": "DIQRA Architects is a design-focused practice committed to creating spaces that inspire, function beautifully, and respond thoughtfully to their context.",
          "url": "https://diqraarchitects.com",
          "foundingDate": "2020",
          "numberOfEmployees": "10-50",
          "knowsAbout": [
            "Residential Architecture",
            "Commercial Architecture",
            "Interior Design",
            "Sustainable Design",
            "Urban Planning"
          ],
          "hasCredential": [
            {
              "@type": "EducationalOccupationalCredential",
              "credentialCategory": "Professional License",
              "name": "Licensed Architect"
            }
          ],
          "areaServed": {
            "@type": "Country",
            "name": "United States"
          }
        }
      }
    </script>
</head>

<body class="about-page">
  <!-- New Dynamic Navbar -->
  <nav class="new-navbar" id="navbar">
    <div class="new-logo">
      <a href="index.html">
        <img src="Diqra.png" alt="Diqra Architects" />
      </a>
    </div>
  </nav>

  <!-- Sticky Buttons Container -->
  <div class="sticky-buttons-container">
    <button class="get-in-touch-btn sticky-button" id="get-in-touch-btn">
      <span>Get in Touch</span>
    </button>
    <button class="sticky-menu-btn sticky-button" id="sticky-menu-btn">
      <span>Menu</span>
    </button>
  </div>

  <!-- New Menu Overlay -->
  <div class="new-menu-overlay" id="new-menu-overlay">
    <div class="new-menu-content">
      <div class="new-menu-items">
        <div class="col-lg">
          <div class="new-menu-preview-img">
            <!-- Default preview image -->
            <img src="https://cdn.cosmos.so/0098a074-f8a2-4821-bcb0-433c093ae255.jpeg" alt="Diqra Architecture About" />
          </div>
        </div>
        <div class="col-sm">
          <div class="new-menu-links">
            <div class="new-link">
              <a href="/index.html" data-img="https://cdn.cosmos.so/94579ea4-daee-43f9-b778-84156b731361.jpeg">Home</a>
            </div>
            <div class="new-link">
              <a href="/about.html" data-img="https://cdn.cosmos.so/0098a074-f8a2-4821-bcb0-433c093ae255.jpeg">About</a>
            </div>
            <div class="new-link">
              <a href="process.html"
                data-img="https://cdn.cosmos.so/ce9f9fd7-a2a5-476d-9757-481ca01b5861.jpeg">Process</a>
            </div>
            <div class="new-link">
              <a href="works.html" data-img="https://cdn.cosmos.so/5f8d5539-943c-4df5-bae8-8e714633ddd0.jpeg">Works</a>
            </div>
            <div class="new-link">
              <a href="gallery.html"
                data-img="https://cdn.cosmos.so/f733585a-081e-48e7-a30e-e636446f2168.jpeg">Gallery</a>
            </div>
          </div>
          <div class="new-menu-socials">
            <div class="new-social">
              <a href="#contact">Get in Touch</a>
            </div>
            <div class="new-social">
              <a href="/process.html">Process</a>
            </div>
          </div>
        </div>
      </div>
      <div class="new-menu-footer">
        <div class="col-lg">
          <a href="index.html#projects">View Projects</a>
        </div>
        <div class="col-sm">
          <a href="about.html">Our Story</a>
          <a href="#contact">Start Project</a>
        </div>
      </div>
    </div>
  </div>

  <!-- New Container for 3D Transformation -->
  <div class="new-container" id="new-container">
    <!-- Main Content -->
    <main id="main-content" class="main-content">
      <!-- About Hero Section -->
      <section class="about-hero" role="banner" aria-label="About us hero section">
        <div class="about-hero-content">
          <div class="about-hero-text">
            <h1 class="about-title">DIQRA</h1>
            <!-- <p class="about-subtitle">
              DIQRA Architects is a design-focused practice committed to
              creating spaces that inspire, function beautifully, and respond
              thoughtfully to their context.
            </p> -->
          </div>
          <div class="about-hero-image">
            <img
              src="https://images.unsplash.com/photo-1448630360428-65456885c650?q=80&w=867&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA=="
              alt="DIQRA Architects Studio" />
          </div>
        </div>
      </section>

      <!-- About Content Sections -->
      <section class="about-content">
        <div class="about-section">
          <div class="about-section-content">
            <p>
              At DIQRA, we take a collaborative approach. Whether we’re
              working in the studio or alongside our clients and partners,
              it’s this shared process that helps us create work that reflects
              both your vision and ours.
            </p>
          </div>
        </div>
      </section>

      <section class="about-content">
        <div class="second-about">
          <div class="leftabout">
            <img
              src="https://images.unsplash.com/photo-1448630360428-65456885c650?q=80&w=867&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx"
              alt="" />
          </div>
          <div class="rightabout">
            <img
              src="https://images.unsplash.com/photo-1448630360428-65456885c650?q=80&w=867&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx"
              alt="" />
            <p class="about-second-section-content">
              We believe that architecture is a reflection of the people who
              inhabit it. Our goal is to create spaces that are not only
              beautiful but also functional and meaningful. We listen to your
              needs, understand your vision, and work with you to bring it to
              life.
            </p>
          </div>
        </div>
      </section>

      <!-- What We Offer Section -->
      <section class="services-offer-section">
        <div class="services-offer-container">
          <div class="services-offer-header">
            <h2 class="services-offer-title">What We Offer</h2>
            <p class="services-offer-subtitle">
              Comprehensive architectural solutions tailored to bring your
              vision to life
            </p>
          </div>

          <div class="services-offer-grid">
            <a href="construction.html" class="service-offer-item">
              <div class="service-offer-icon">
                <div class="service-offer-number">01</div>
              </div>
              <div class="service-offer-content">
                <h3 class="service-offer-title">Construction</h3>
                <p class="service-offer-description">
                  From foundation to finish, we oversee every aspect of
                  construction to ensure your project meets the highest
                  standards of quality and craftsmanship.
                </p>
              </div>
            </a>

            <a href="interior.html" class="service-offer-item">
              <div class="service-offer-icon">
                <div class="service-offer-number">02</div>
              </div>
              <div class="service-offer-content">
                <h3 class="service-offer-title">Interior Design</h3>
                <p class="service-offer-description">
                  Creating beautiful, functional interior spaces that reflect
                  your personality and enhance your daily living experience.
                </p>
              </div>
            </a>

            <a href="renovation.html" class="service-offer-item">
              <div class="service-offer-icon">
                <div class="service-offer-number">03</div>
              </div>
              <div class="service-offer-content">
                <h3 class="service-offer-title">Renovation</h3>
                <p class="service-offer-description">
                  Transforming existing spaces with innovative design
                  solutions that breathe new life into your home or commercial
                  property.
                </p>
              </div>
            </a>

            <a href="project-management.html" class="service-offer-item">
              <div class="service-offer-icon">
                <div class="service-offer-number">04</div>
              </div>
              <div class="service-offer-content">
                <h3 class="service-offer-title">Project Management</h3>
                <p class="service-offer-description">
                  Seamless coordination of all project phases, ensuring timely
                  delivery and budget adherence while maintaining exceptional
                  quality standards.
                </p>
              </div>
            </a>

            <a href="consultation.html" class="service-offer-item">
              <div class="service-offer-icon">
                <div class="service-offer-number">05</div>
              </div>
              <div class="service-offer-content">
                <h3 class="service-offer-title">Consultation</h3>
                <p class="service-offer-description">
                  Expert architectural guidance and strategic planning to help
                  you make informed decisions throughout your project journey.
                </p>
              </div>
            </a>
          </div>
        </div>
      </section>

      <!-- Meet the team  -->
      <section class="team-section">
        <div class="team-container">
          <div class="team-content">
            <div class="team-left">
              <h2 class="team-title">Meet the Team</h2>
              <div class="team-members">
                <div class="team-member active" data-member="sarah">
                  <h3 class="member-name">Naveen</h3>
                  <p class="member-role">Co- Founder & CEO</p>
                  <p class="member-description">
                    With over 15 years of experience in sustainable design,
                    Sarah leads our residential and commercial projects with a
                    focus on environmental responsibility and innovative
                    solutions.
                  </p>
                </div>

                <div class="team-member" data-member="michael">
                  <h3 class="member-name">Jafar Sathik</h3>
                  <p class="member-role">Co- Founder & Principal Architect</p>
                  <p class="member-description">
                    Michael brings a unique perspective to interior design and
                    spatial planning, combining modern aesthetics with
                    functional excellence to create inspiring environments.
                  </p>
                </div>
              </div>
            </div>

            <div class="team-right">
              <div class="team-image-container">
                <img src="https://diqraarchitects.com/wp-content/uploads/2024/12/Nave.jpg" alt="Naveen"
                  class="team-image active" data-member="sarah" />
                <img src="https://diqraarchitects.com/wp-content/uploads/2024/11/Jaf.jpg" alt="Jafar Sathik"
                  class="team-image" data-member="michael" />
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- CTA Section -->
      <section class="cta-section">
        <div class="cta-container">
          <div class="cta-content">
            <div class="cta-title">
              <div class="cta-title-line1">FOCUSED ON QUALITY</div>
              <div class="cta-title-line2">DRIVEN BY CREATIVITY</div>
            </div>
            <button class="cta-button get-in-touch-btn" id="cta-button"
              onclick="document.getElementById('contact-sidebar').classList.add('active')">
              <span>Tell us about your project</span>
            </button>
          </div>
        </div>
      </section>
    </main>
  </div>
  <!-- End New Container -->

  <!-- Footer -->
  <footer class="footer">
    <div class="footer-container">
      <!-- Left: Logo -->
      <div class="footer-left">
        <div class="footer-logo">
          <img class="footer-logo-image1"
            src="https://images.unsplash.com/photo-1448630360428-65456885c650?q=80&w=867&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx"
            alt="" />
          <img class="footer-logo-image2" src="Diqra.png" alt="Diqra Architects Logo" />
        </div>
      </div>

      <!-- Center: Navigation -->
      <div class="footer-center">
        <nav class="footer-nav">
          <a href="index.html" class="footer-nav-link">Home</a>
          <a href="about.html" class="footer-nav-link">About</a>
          <a href="process.html" class="footer-nav-link">Process</a>
          <a href="works.html" class="footer-nav-link">Works</a>
          <a href="gallery.html" class="footer-nav-link">Gallery</a>
        </nav>
      </div>

      <!-- Right: Address -->
      <div class="footer-right">
        <!-- Acknowledgement  -->
        <div class="footer-acknowledgement">
          <h1>Acknowledgement</h1>
          <p>
            We respectfully acknowledge the Turrbal people, the Traditional
            Owners and Custodians of the Country on which we work. We pay our
            respects to Elders past and present, and acknowledge their
            continuing connection to land, sea and community.
          </p>
        </div>

        <div class="footer-address">
          <h4>GET IN TOUCH</h4>
          <p>
            123 Architecture Street<br />
            Design City, State 12345
          </p>
          <p>
            Phone: +****************<br />
            Email: <EMAIL>
          </p>
        </div>
      </div>
    </div>

    <!-- Footer Bottom -->
    <div class="footer-bottom">
      <p>&copy; 2024 DIQRA Architects. All rights reserved.</p>
    </div>
  </footer>

  <!-- Contact Sidebar -->
  <div class="contact-sidebar" id="contact-sidebar">
    <div class="sidebar-overlay" id="sidebar-overlay"></div>
    <div class="contact-sidebar-content">
      <div class="sidebar-header">
        <h2>Get in Touch</h2>
        <button class="sidebar-close" id="sidebar-close">×</button>
      </div>

      <div class="sidebar-body">
        <p class="sidebar-subtitle">
          Ready to start your project? Let's discuss your vision.
        </p>

        <form class="contact-form" id="contact-form">
          <div class="input-group">
            <input type="text" id="name" name="name" required />
            <label for="name">Your Name</label>
          </div>

          <div class="input-group">
            <input type="email" id="email" name="email" required />
            <label for="email">Email Address</label>
          </div>

          <div class="input-group">
            <input type="tel" id="phone" name="phone" />
            <label for="phone">Phone Number</label>
          </div>

          <div class="input-group">
            <select id="project-type" name="project-type" required>
              <option value="">Select Project Type</option>
              <option value="new-home">New Home</option>
              <option value="renovation">Renovation</option>
              <option value="commercial">Commercial</option>
              <option value="interior">Interior Design</option>
              <option value="other">Other</option>
            </select>
            <label for="project-type">Project Type</label>
          </div>

          <div class="input-group">
            <textarea id="message" name="message" rows="4"></textarea>
            <label for="message">Project Details</label>
          </div>

          <button type="submit" class="submit-btn">Send Message</button>
        </form>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <!-- EmailJS -->
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>

  <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/gsap.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/CustomEase.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/Flip.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/ScrollTrigger.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/ScrollToPlugin.min.js"></script>

  <script src="new-navbar.js"></script>
  <script src="services.js"></script>
  <script src="animations.js"></script>
  <script src="contact-sidebar.js"></script>

  <script>
    // Ensure about page content is visible immediately
    document.addEventListener("DOMContentLoaded", () => {
      console.log("About page loaded");

      // Force show main content
      const mainContent = document.getElementById("main-content");
      if (mainContent) {
        mainContent.style.display = "block";
        mainContent.style.opacity = "1";
        mainContent.style.transform = "translateY(0)";
      }

      // About page navbar is now handled by about-navbar.js

      // CTA button functionality is handled by onclick in HTML

      // Team section hover functionality with GSAP
      const teamMembers = document.querySelectorAll(".team-member");
      const teamImages = document.querySelectorAll(".team-image");

      // Initialize team section
      function initializeTeamSection() {
        // Set initial states
        teamMembers.forEach((member, index) => {
          if (index === 0) {
            // First member active by default
            gsap.set(member, { opacity: 1 });
            gsap.set(member.querySelector(".member-name"), {
              color: "#2a4d3b",
            });
          } else {
            gsap.set(member, { opacity: 0.6 });
            gsap.set(member.querySelector(".member-name"), { color: "#111" });
          }
        });

        // Set initial image states
        teamImages.forEach((img, index) => {
          if (index === 0) {
            gsap.set(img, { opacity: 1, scale: 1 });
          } else {
            gsap.set(img, { opacity: 0, scale: 1.05 });
          }
        });
      }

      // Team member hover animations
      teamMembers.forEach((member) => {
        member.addEventListener("mouseenter", () => {
          const memberData = member.getAttribute("data-member");
          const memberName = member.querySelector(".member-name");
          const correspondingImage = document.querySelector(
            `.team-image[data-member="${memberData}"]`
          );

          // Animate all members to inactive state
          teamMembers.forEach((m) => {
            const name = m.querySelector(".member-name");
            gsap.to(m, { opacity: 0.6, duration: 0.3, ease: "power2.out" });
            gsap.to(name, {
              color: "#111",
              duration: 0.3,
              ease: "power2.out",
            });
          });

          // Animate current member to active state
          gsap.to(member, { opacity: 1, duration: 0.3, ease: "power2.out" });
          gsap.to(memberName, {
            color: "#2a4d3b",
            duration: 0.3,
            ease: "power2.out",
          });

          // Animate all images to hidden state
          teamImages.forEach((img) => {
            gsap.to(img, {
              opacity: 0,
              scale: 1.05,
              duration: 0.4,
              ease: "power2.out",
            });
          });

          // Animate corresponding image to visible state
          if (correspondingImage) {
            gsap.to(correspondingImage, {
              opacity: 1,
              scale: 1,
              duration: 0.5,
              delay: 0.2,
              ease: "power2.out",
            });
          }
        });
      });

      // Initialize the team section
      if (teamMembers.length > 0) {
        initializeTeamSection();

        // Animate team section on scroll

        // Animate team members individually
        gsap.fromTo(
          ".team-member",
          {
            opacity: 0,
            x: -30,
          },
          {
            opacity: 0.6, // Default opacity for non-active members
            x: 0,
            duration: 0.6,
            stagger: 0.2,
            ease: "power2.out",
            scrollTrigger: {
              trigger: ".team-section",
              start: "top 70%",
              end: "bottom 30%",
              toggleActions: "play none none reverse",
            },
            onComplete: () => {
              // Ensure first member is properly highlighted after animation
              const firstMember = teamMembers[0];
              const firstName = firstMember?.querySelector(".member-name");
              if (firstMember && firstName) {
                gsap.set(firstMember, { opacity: 1 });
                gsap.set(firstName, { color: "#2a4d3b" });
              }
            },
          }
        );

        // Animate team images
        gsap.fromTo(
          ".team-image-container",
          {
            opacity: 0,
            scale: 0.9,
          },
          {
            opacity: 1,
            scale: 1,
            duration: 0.8,
            delay: 0.3,
            ease: "power2.out",
            scrollTrigger: {
              trigger: ".team-section",
              start: "top 70%",
              end: "bottom 30%",
              toggleActions: "play none none reverse",
            },
          }
        );
      }

      // Animate hero section

      // CTA Section Animation
      gsap.fromTo(
        ".cta-content",
        {
          opacity: 0,
          y: 50,
        },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power2.out",
          scrollTrigger: {
            trigger: ".cta-section",
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );

      // Footer Animation
      gsap.fromTo(
        ".footer",
        {
          opacity: 0,
          y: 30,
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power2.out",
          scrollTrigger: {
            trigger: ".footer",
            start: "top 85%",
            end: "bottom 15%",
            toggleActions: "play none none reverse",
          },
        }
      );
    });
  </script>
</body>

</html>