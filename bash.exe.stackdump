Stack trace:
Frame         Function      Args
0007FFFF9890  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8790) msys-2.0.dll+0x2118E
0007FFFF9890  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFF9B68) msys-2.0.dll+0x69BA
0007FFFF9890  0002100469F2 (00021028DF99, 0007FFFF9748, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9890  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9890  00021006A545 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFF9B70  00021006B9A5 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF80A980000 ntdll.dll
7FF808B20000 KERNEL32.DLL
7FF807DF0000 KERNELBASE.dll
7FF8088A0000 USER32.dll
7FF807D10000 win32u.dll
7FF809FE0000 GDI32.dll
000210040000 msys-2.0.dll
7FF8081E0000 gdi32full.dll
7FF807D40000 msvcp_win.dll
7FF808450000 ucrtbase.dll
7FF808E50000 advapi32.dll
7FF80A240000 msvcrt.dll
7FF808A70000 sechost.dll
7FF80A330000 RPCRT4.dll
7FF806FE0000 CRYPTBASE.DLL
7FF808320000 bcryptPrimitives.dll
7FF80A2F0000 IMM32.DLL
