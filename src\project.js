// Project Page JavaScript - Parallax and Animations
document.addEventListener("DOMContentLoaded", () => {
  // Register GSAP plugins
  gsap.registerPlugin(ScrollTrigger);

  // Parallax effect for hero background
  const heroBackground = document.querySelector('.hero-bg-image');

  if (heroBackground) {
    gsap.to(heroBackground, {
      yPercent: -50,
      ease: "none",
      scrollTrigger: {
        trigger: ".project-hero",
        start: "top bottom",
        end: "bottom top",
        scrub: true
      }
    });
  }

  // Fade in animations for hero content
  // const heroContent = document.querySelector('.project-hero-content');

  // if (heroContent) {
  //   gsap.fromTo(heroContent,
  //     {
  //       opacity: 0,
  //       y: 50
  //     },
  //     {
  //       opacity: 1,
  //       y: 0,
  //       duration: 1.5,
  //       ease: "power2.out",
  //       delay: 0.5
  //     }
  //   );
  // }

  // Animate project description section
  // const descriptionElements = document.querySelectorAll('.description-content h2, .description-content p, .project-details');

  // descriptionElements.forEach((element, index) => {
  //   gsap.fromTo(element,
  //     {
  //       opacity: 0,
  //       y: 30
  //     },
  //     {
  //       opacity: 1,
  //       y: 0,
  //       duration: 0.8,
  //       ease: "power2.out",
  //       scrollTrigger: {
  //         trigger: element,
  //         start: "top 80%",
  //         end: "bottom 20%",
  //         toggleActions: "play none none reverse"
  //       },
  //       delay: index * 0.1
  //     }
  //   );
  // });

  // Gallery image animations
  // const galleryImages = document.querySelectorAll('.gallery-image');

  // galleryImages.forEach((image, index) => {
  //   gsap.fromTo(image,
  //     {
  //       opacity: 0,
  //       scale: 0.95
  //     },
  //     {
  //       opacity: 1,
  //       scale: 1,
  //       duration: 1,
  //       ease: "power2.out",
  //       scrollTrigger: {
  //         trigger: image,
  //         start: "top 85%",
  //         end: "bottom 15%",
  //         toggleActions: "play none none reverse"
  //       },
  //       delay: (index % 3) * 0.1
  //     }
  //   );
  // });

  // CTA section animation
  // const ctaContent = document.querySelector('.cta-content');

  // if (ctaContent) {
  //   gsap.fromTo(ctaContent,
  //     {
  //       opacity: 0,
  //       y: 40
  //     },
  //     {
  //       opacity: 1,
  //       y: 0,
  //       duration: 1,
  //       ease: "power2.out",
  //       scrollTrigger: {
  //         trigger: ctaContent,
  //         start: "top 80%",
  //         end: "bottom 20%",
  //         toggleActions: "play none none reverse"
  //       }
  //     }
  //   );
  // }

  // Smooth scroll for internal links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  // Image lazy loading optimization
  const images = document.querySelectorAll('img');

  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.style.opacity = '0';
        img.onload = () => {
          gsap.to(img, {
            opacity: 1,
            duration: 0.5,
            ease: "power2.out"
          });
        };
        observer.unobserve(img);
      }
    });
  });

  images.forEach(img => {
    imageObserver.observe(img);
  });

  // Add scroll progress indicator
  const progressBar = document.createElement('div');
  progressBar.className = 'scroll-progress-bar';
  progressBar.innerHTML = '<div class="progress-fill"></div>';
  document.body.appendChild(progressBar);

  // Style the progress bar
  const style = document.createElement('style');
  style.textContent = `
    .scroll-progress-bar {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background: rgba(255, 255, 255, 0.2);
      z-index: 9999;
    }
    
    .progress-fill {
      height: 100%;
      background: #2a4d3b;
      width: 0%;
      transition: width 0.1s ease;
    }
  `;
  document.head.appendChild(style);

  // Update progress bar on scroll
  const progressFill = document.querySelector('.progress-fill');

  window.addEventListener('scroll', () => {
    const scrollTop = window.pageYOffset;
    const docHeight = document.body.scrollHeight - window.innerHeight;
    const scrollPercent = (scrollTop / docHeight) * 100;

    if (progressFill) {
      progressFill.style.width = scrollPercent + '%';
    }
  });

  // Add hover effects to gallery images
  const galleryImages = document.querySelectorAll('.gallery-image');
  galleryImages.forEach(image => {
    const img = image.querySelector('img');

    image.addEventListener('mouseenter', () => {
      gsap.to(img, {
        scale: 1.05,
        duration: 0.5,
        ease: "power2.out"
      });
    });

    image.addEventListener('mouseleave', () => {
      gsap.to(img, {
        scale: 1,
        duration: 0.5,
        ease: "power2.out"
      });
    });
  });

  // Add click handlers for CTA buttons
  const ctaButtons = document.querySelectorAll('.cta-btn');

  ctaButtons.forEach(button => {
    button.addEventListener('click', function () {
      // Add click animation
      gsap.to(this, {
        scale: 0.95,
        duration: 0.1,
        ease: "power2.out",
        yoyo: true,
        repeat: 1
      });
    });
  });

  console.log("Project page initialized with parallax and animations");
});
