// Gallery Page JavaScript - Updated for Diqra Architecture
console.log("Gallery.js loaded successfully");

// Register GSAP plugins
gsap.registerPlugin(CustomEase);
CustomEase.create("hop", "0.9, 0, 0.1, 1");

console.log("GSAP plugins registered");

// Architecture project titles
const items = [
  "Modern Villa Residence",
  "Urban Loft Design",
  "Corporate Office Complex",
  "Luxury Penthouse",
  "Minimalist House",
  "Cultural Center",
  "Eco Resort",
  "Glass Pavilion",
  "Riverside Residence",
  "Innovation Hub",
  "Sustainable Tower",
  "Heritage Restoration",
  "Community Center",
  "Boutique Hotel",
  "Art Gallery",
  "Educational Campus",
  "Mixed-Use Development",
  "Wellness Center",
  "Tech Headquarters",
  "Residential Complex",
];

// Architecture project images
const imageUrls = [
  "https://cdn.cosmos.so/94579ea4-daee-43f9-b778-84156b731361.jpeg",
  "https://cdn.cosmos.so/0098a074-f8a2-4821-bcb0-433c093ae255.jpeg",
  "https://cdn.cosmos.so/ce9f9fd7-a2a5-476d-9757-481ca01b5861.jpeg",
  "https://cdn.cosmos.so/5f8d5539-943c-4df5-bae8-8e714633ddd0.jpeg",
  "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=800",
  "https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?w=800",
  "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800",
  "https://images.unsplash.com/photo-1600607687644-aac4c3eac7f4?w=800",
  "https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?w=800",
  "https://images.unsplash.com/photo-1600607688969-a5bfcd646154?w=800",
];

// Updated selectors for namespaced classes
const container = document.querySelector(".gallery-container");
const canvas = document.getElementById("gallery-canvas");
const overlay = document.getElementById("gallery-overlay");
const projectTitleElement = document.querySelector(".gallery-project-title p");
const instructionsOverlay = document.getElementById("gallery-instructions");

// Mobile detection utility
const isMobile = () => {
  return (
    window.innerWidth <= 768 ||
    "ontouchstart" in window ||
    navigator.maxTouchPoints > 0
  );
};

const isTablet = () => {
  return window.innerWidth <= 1024 && window.innerWidth > 768;
};

// Settings object for Tweakpane - Optimized for performance and mobile
const settings = {
  // Item sizes - responsive based on screen size (reduced for better performance)
  baseWidth: isMobile() ? 250 : isTablet() ? 320 : 360,
  smallHeight: isMobile() ? 200 : isTablet() ? 250 : 280,
  largeHeight: isMobile() ? 320 : isTablet() ? 380 : 420,
  itemGap: isMobile() ? 35 : isTablet() ? 45 : 55,
  hoverScale: isMobile() ? 1.0 : 1.05,
  expandedScale: isMobile() ? 0.6 : 0.4,
  dragEase: isMobile() ? 0.25 : 0.15, // Slower for touch devices
  momentumFactor: isMobile() ? 150 : 200,
  bufferZone: isMobile() ? 1 : 2, // Smaller buffer for mobile performance
  borderRadius: isMobile() ? 12 : 0,
  vignetteSize: 0,
  // Page vignette settings
  vignetteStrength: isMobile() ? 0.5 : 0.7,
  vignetteSize: isMobile() ? 100 : 200,
  // Overlay settings
  overlayOpacity: 0.9,
  overlayEaseDuration: 0.8,
  // Animation durations
  zoomDuration: isMobile() ? 0.4 : 0.6,
};

// Define item sizes based on settings
let itemSizes = [
  {
    width: settings.baseWidth,
    height: settings.smallHeight,
  },
  {
    width: settings.baseWidth,
    height: settings.largeHeight,
  },
];

let itemGap = settings.itemGap;
let columns = isMobile() ? 2 : isTablet() ? 3 : 4; // Responsive column count
const itemCount = items.length;

// Calculate grid cell size based on the largest possible item
let cellWidth = settings.baseWidth + settings.itemGap;
let cellHeight =
  Math.max(settings.smallHeight, settings.largeHeight) + settings.itemGap;

let isDragging = false;
let startX, startY;
let targetX = 0,
  targetY = 0;
let currentX = 0,
  currentY = 0;
let dragVelocityX = 0,
  dragVelocityY = 0;
let lastDragTime = 0;
let mouseHasMoved = false;
let visibleItems = new Set();
let lastUpdateTime = 0;
let lastX = 0,
  lastY = 0;
let isExpanded = false;
let activeItem = null;
let activeItemId = null;
let canDrag = true;
let originalPosition = null;
let expandedItem = null;
let overlayAnimation = null;
let titleSplit = null;
let activeCaptionNameSplit = null;
let activeCaptionNumberSplit = null;
let paneInstance = null;
let instructionsShown = false;

// --- DRAG TO PAN FUNCTIONALITY ---
let isPointerDown = false;
let dragStartX = 0,
  dragStartY = 0;
let dragOriginX = 0,
  dragOriginY = 0;

// Image-based dragging - much more intuitive
function setupImageDragging() {
  // Remove old canvas event listeners
  canvas.removeEventListener("mousedown", handleMouseDown);

  // We'll add event listeners to individual images when they're created
}

function handleMouseDown(e) {
  // Only allow dragging if clicking on a gallery item
  const galleryItem = e.target.closest(".gallery-item");
  if (!galleryItem) return;

  e.preventDefault();
  isPointerDown = true;
  dragStartX = e.clientX;
  dragStartY = e.clientY;
  dragOriginX = targetX;
  dragOriginY = targetY;
  container.classList.add("dragging");
  document.body.style.userSelect = "none";
}

window.addEventListener("mousemove", (e) => {
  if (!isPointerDown) return;
  e.preventDefault();

  const dx = e.clientX - dragStartX;
  const dy = e.clientY - dragStartY;
  targetX = dragOriginX + dx;
  targetY = dragOriginY + dy;
});

window.addEventListener("mouseup", (e) => {
  isPointerDown = false;
  container.classList.remove("dragging");
  document.body.style.userSelect = "";
});

// Enhanced touch support for gallery items with mobile optimization
function handleTouchStart(e) {
  const galleryItem = e.target.closest(".gallery-item");
  if (!galleryItem) return;

  if (e.touches.length !== 1) return;

  // Only prevent default if we're actually dragging, not for taps
  const touch = e.touches[0];
  isPointerDown = true;
  dragStartX = touch.clientX;
  dragStartY = touch.clientY;
  dragOriginX = targetX;
  dragOriginY = targetY;

  // Add a small delay to distinguish between tap and drag
  setTimeout(() => {
    if (isPointerDown) {
      container.classList.add("dragging");
      e.preventDefault();
    }
  }, 100);
}

// Enhanced touch move with momentum and better performance
window.addEventListener(
  "touchmove",
  (e) => {
    if (!isPointerDown || e.touches.length !== 1) return;

    const touch = e.touches[0];
    const dx = touch.clientX - dragStartX;
    const dy = touch.clientY - dragStartY;

    // Only prevent default if we've moved significantly (to allow scrolling)
    if (Math.abs(dx) > 10 || Math.abs(dy) > 10) {
      e.preventDefault();
      container.classList.add("dragging");
    }

    targetX = dragOriginX + dx;
    targetY = dragOriginY + dy;
  },
  { passive: false }
);

// Enhanced touch end with momentum
window.addEventListener("touchend", (e) => {
  isPointerDown = false;
  container.classList.remove("dragging");

  // Add momentum for mobile devices
  if (isMobile() && Math.abs(targetX - dragOriginX) > 50) {
    const momentum = (targetX - dragOriginX) * 0.3;
    targetX += momentum;
  }
});

// Gallery Instructions Management
function showInstructions() {
  if (instructionsOverlay && !instructionsShown) {
    instructionsOverlay.classList.remove("hidden");
    instructionsShown = true;

    // Auto-hide after 4 seconds
    setTimeout(() => {
      hideInstructions();
    }, 4000);

    // Hide on any interaction
    const hideOnInteraction = () => {
      hideInstructions();
      document.removeEventListener("click", hideOnInteraction);
      document.removeEventListener("touchstart", hideOnInteraction);
      document.removeEventListener("keydown", hideOnInteraction);
    };

    // Add event listeners to hide instructions
    setTimeout(() => {
      document.addEventListener("click", hideOnInteraction);
      document.addEventListener("touchstart", hideOnInteraction);
      document.addEventListener("keydown", hideOnInteraction);
    }, 1000); // Wait 1 second before allowing interaction to hide
  }
}

function hideInstructions() {
  if (instructionsOverlay) {
    instructionsOverlay.classList.add("hidden");
  }
}

// Prevent context menu and selection
canvas.addEventListener("contextmenu", (e) => {
  e.preventDefault();
});

canvas.addEventListener("selectstart", (e) => {
  e.preventDefault();
});

canvas.addEventListener("dragstart", (e) => {
  e.preventDefault();
});

// Initialize Tweakpane
function initTweakpane() {
  if (!window.Tweakpane) {
    setTimeout(initTweakpane, 100);
    return;
  }

  paneInstance = new window.Tweakpane.Pane({
    title: "Gallery Settings",
    expanded: false,
  });

  const paneElement = paneInstance.element;
  paneElement.style.position = "fixed";
  paneElement.style.top = "10px";
  paneElement.style.right = "10px";
  paneElement.style.zIndex = "10000";

  // Item size settings
  const sizeFolder = paneInstance.addFolder({
    title: "Item Sizes",
    expanded: false,
  });

  sizeFolder
    .addBinding(settings, "baseWidth", {
      min: 100,
      max: 600,
      step: 10,
    })
    .on("change", updateSettings);

  sizeFolder
    .addBinding(settings, "smallHeight", {
      min: 100,
      max: 400,
      step: 10,
    })
    .on("change", updateSettings);

  sizeFolder
    .addBinding(settings, "largeHeight", {
      min: 100,
      max: 600,
      step: 10,
    })
    .on("change", updateSettings);

  // Layout settings
  const layoutFolder = paneInstance.addFolder({
    title: "Layout",
    expanded: false,
  });

  layoutFolder
    .addBinding(settings, "itemGap", {
      min: 0,
      max: 100,
      step: 5,
    })
    .on("change", updateSettings);

  layoutFolder
    .addBinding(settings, "bufferZone", {
      min: 1,
      max: 5,
      step: 0.5,
    })
    .on("change", updateSettings);

  // Style settings
  const styleFolder = paneInstance.addFolder({
    title: "Style",
    expanded: false,
  });

  styleFolder
    .addBinding(settings, "borderRadius", {
      min: 0,
      max: 16,
      step: 1,
    })
    .on("change", updateBorderRadius);

  // Page vignette settings
  const pageVignetteFolder = paneInstance.addFolder({
    title: "Page Vignette",
    expanded: false,
  });

  pageVignetteFolder
    .addBinding(settings, "vignetteStrength", {
      min: 0,
      max: 1,
      step: 0.05,
    })
    .on("change", updatePageVignette);

  pageVignetteFolder
    .addBinding(settings, "vignetteSize", {
      min: 0,
      max: 500,
      step: 10,
    })
    .on("change", updatePageVignette);

  // Animation settings
  const animationFolder = paneInstance.addFolder({
    title: "Animation",
    expanded: false,
  });

  animationFolder
    .addBinding(settings, "hoverScale", {
      min: 1,
      max: 1.5,
      step: 0.05,
    })
    .on("change", updateHoverScale);

  animationFolder.addBinding(settings, "expandedScale", {
    min: 0.2,
    max: 0.8,
    step: 0.05,
  });

  animationFolder.addBinding(settings, "dragEase", {
    min: 0.01,
    max: 0.2,
    step: 0.01,
  });

  // Reset view button
  paneInstance
    .addButton({
      title: "Reset View",
    })
    .on("click", () => {
      targetX = 0;
      targetY = 0;
    });

  // Keyboard shortcut for toggling the panel
  window.addEventListener("keydown", (e) => {
    if (e.key === "h" || e.key === "H") {
      togglePaneVisibility();
    }
  });
}

// Toggle panel visibility
function togglePaneVisibility() {
  if (!paneInstance) return;
  const element = paneInstance.element;
  if (element.style.display === "none") {
    element.style.display = "";
  } else {
    element.style.display = "none";
  }
}

// Update CSS variables
function updateBorderRadius() {
  document.documentElement.style.setProperty(
    "--gallery-border-radius",
    `${settings.borderRadius}px`
  );
}

function updateVignetteSize() {
  document.documentElement.style.setProperty(
    "--gallery-vignette-size",
    `${settings.vignetteSize}px`
  );
}

function updatePageVignette() {
  const strength = settings.vignetteStrength;
  const size = settings.vignetteSize;

  // Regular vignette
  const regularOpacity = strength * 0.7;
  const regularSize = size * 1.5;
  document.documentElement.style.setProperty(
    "--gallery-page-vignette-size",
    `${regularSize}px`
  );
  document.documentElement.style.setProperty(
    "--gallery-page-vignette-color",
    `rgba(0,0,0,${regularOpacity})`
  );

  // Strong vignette
  const strongOpacity = strength * 0.85;
  const strongSize = size * 0.75;
  document.documentElement.style.setProperty(
    "--gallery-page-vignette-strong-size",
    `${strongSize}px`
  );
  document.documentElement.style.setProperty(
    "--gallery-page-vignette-strong-color",
    `rgba(0,0,0,${strongOpacity})`
  );

  // Extreme vignette
  const extremeOpacity = strength;
  const extremeSize = size * 0.4;
  document.documentElement.style.setProperty(
    "--gallery-page-vignette-extreme-size",
    `${extremeSize}px`
  );
  document.documentElement.style.setProperty(
    "--gallery-page-vignette-extreme-color",
    `rgba(0,0,0,${extremeOpacity})`
  );
}

function updateHoverScale() {
  document.documentElement.style.setProperty(
    "--gallery-hover-scale",
    settings.hoverScale
  );
}

// Update settings and rebuild the grid
function updateSettings() {
  itemSizes = [
    {
      width: settings.baseWidth,
      height: settings.smallHeight,
    },
    {
      width: settings.baseWidth,
      height: settings.largeHeight,
    },
  ];
  itemGap = settings.itemGap;
  columns = 4;

  cellWidth = settings.baseWidth + settings.itemGap;
  cellHeight =
    Math.max(settings.smallHeight, settings.largeHeight) + settings.itemGap;

  // Clear existing items and rebuild
  visibleItems.forEach((itemId) => {
    const item = document.getElementById(itemId);
    if (item && item.parentNode === canvas) {
      canvas.removeChild(item);
    }
  });
  visibleItems.clear();
  updateVisibleItems();

  updateBorderRadius();
  updateVignetteSize();
  updateHoverScale();
  updatePageVignette();
}

// Title animation functions
function setAndAnimateTitle(title) {
  if (titleSplit) titleSplit.revert();
  projectTitleElement.textContent = title;
  titleSplit = new SplitType(projectTitleElement, {
    types: "words",
  });
  gsap.set(titleSplit.words, {
    y: "100%",
  });
}

function animateTitleIn() {
  gsap.fromTo(
    titleSplit.words,
    {
      y: "100%",
      opacity: 0,
    },
    {
      y: "0%",
      opacity: 1,
      duration: 1,
      stagger: 0.1,
      ease: "power3.out",
    }
  );
}

function animateTitleOut() {
  gsap.to(titleSplit.words, {
    y: "-100%",
    opacity: 0,
    duration: 1,
    stagger: 0.1,
    ease: "power3.out",
  });
}

// Overlay animation functions
function animateOverlayIn() {
  if (overlayAnimation) overlayAnimation.kill();
  overlayAnimation = gsap.to(overlay, {
    opacity: settings.overlayOpacity,
    duration: settings.overlayEaseDuration,
    ease: "power2.inOut",
    overwrite: true,
  });
}

function animateOverlayOut() {
  if (overlayAnimation) overlayAnimation.kill();
  overlayAnimation = gsap.to(overlay, {
    opacity: 0,
    duration: settings.overlayEaseDuration,
    ease: "power2.inOut",
  });
}

// Grid utility functions
function getItemSize(row, col) {
  const sizeIndex = Math.abs((row * columns + col) % itemSizes.length);
  return itemSizes[sizeIndex];
}

function getItemId(col, row) {
  return `gallery-item-${col}-${row}`;
}

function getItemPosition(col, row) {
  const xPos = col * cellWidth;
  const yPos = row * cellHeight;
  return { x: xPos, y: yPos };
}

// Optimized visible items update with reduced calculations
let lastUpdateX = 0;
let lastUpdateY = 0;
const updateThreshold = 25; // Reduced threshold for more responsive updates

function updateVisibleItems() {
  // Skip update if we haven't moved enough (but allow first update)
  const deltaX = Math.abs(currentX - lastUpdateX);
  const deltaY = Math.abs(currentY - lastUpdateY);

  if (
    deltaX < updateThreshold &&
    deltaY < updateThreshold &&
    visibleItems.size > 0
  ) {
    return;
  }

  lastUpdateX = currentX;
  lastUpdateY = currentY;

  const buffer = settings.bufferZone;
  const viewWidth = window.innerWidth * (1 + buffer);
  const viewHeight = window.innerHeight * (1 + buffer);

  const startCol = Math.floor((-currentX - viewWidth / 2) / cellWidth);
  const endCol = Math.ceil((-currentX + viewWidth * 1.5) / cellWidth);
  const startRow = Math.floor((-currentY - viewHeight / 2) / cellHeight);
  const endRow = Math.ceil((-currentY + viewHeight * 1.5) / cellHeight);

  const currentItems = new Set();

  // Create document fragment for batch DOM insertion
  const fragment = document.createDocumentFragment();
  const newItems = [];

  // Create or update visible items
  for (let row = startRow; row <= endRow; row++) {
    for (let col = startCol; col <= endCol; col++) {
      const itemId = getItemId(col, row);
      currentItems.add(itemId);

      if (visibleItems.has(itemId)) continue;
      if (activeItemId === itemId && isExpanded) continue;

      const itemSize = getItemSize(row, col);
      const position = getItemPosition(col, row);
      const itemNum = Math.abs((row * columns + col) % itemCount);

      // Create the item element with optimized styles
      const item = document.createElement("div");
      item.className = "gallery-item";
      item.id = itemId;

      // Set all styles at once to reduce reflows
      item.style.cssText = `
        width: ${itemSize.width}px;
        height: ${itemSize.height}px;
        left: ${position.x}px;
        top: ${position.y}px;
        will-change: transform;
      `;

      item.dataset.col = col;
      item.dataset.row = row;
      item.dataset.width = itemSize.width;
      item.dataset.height = itemSize.height;

      // Create image container
      const imageContainer = document.createElement("div");
      imageContainer.className = "gallery-item-image-container";

      // Create image with optimized loading
      const img = document.createElement("img");
      img.src = imageUrls[itemNum % imageUrls.length];
      img.alt = items[itemNum % items.length];
      img.loading = "lazy"; // Native lazy loading
      img.style.cssText = `
        width: 100%;
        height: 100%;
        object-fit: cover;
      `;
      imageContainer.appendChild(img);

      // Create caption
      const caption = document.createElement("div");
      caption.className = "gallery-item-caption";

      const itemName = document.createElement("div");
      itemName.className = "gallery-item-name";
      itemName.textContent = items[itemNum % items.length];

      const itemNumber = document.createElement("div");
      itemNumber.className = "gallery-item-number";
      itemNumber.textContent = `${String(itemNum + 1).padStart(2, "0")}`;

      caption.appendChild(itemName);
      caption.appendChild(itemNumber);

      item.appendChild(imageContainer);
      item.appendChild(caption);

      // Add click handler
      item.addEventListener("click", () => handleItemClick(item, itemNum));

      // Add drag handlers to the item
      item.addEventListener("mousedown", handleMouseDown);
      item.addEventListener("touchstart", handleTouchStart);

      fragment.appendChild(item);
      newItems.push(itemId);
    }
  }

  // Batch insert all new items
  if (fragment.children.length > 0) {
    canvas.appendChild(fragment);
    newItems.forEach((itemId) => visibleItems.add(itemId));
  }

  // Remove items that are no longer visible
  visibleItems.forEach((itemId) => {
    if (!currentItems.has(itemId) || (activeItemId === itemId && isExpanded)) {
      const item = document.getElementById(itemId);
      if (item && item.parentNode === canvas) {
        canvas.removeChild(item);
      }
      visibleItems.delete(itemId);
    }
  });
}

// Handle item click
function handleItemClick(item, itemIndex) {
  if (isExpanded) {
    if (expandedItem) closeExpandedItem();
  } else {
    expandItem(item, itemIndex);
  }
}

// Expand item functionality (simplified for now)
function expandItem(item, itemIndex) {
  // Future implementation for item expansion
  console.log(`Expanding item: ${items[itemIndex]}`);
}

function closeExpandedItem() {
  // Future implementation for closing expanded item
  console.log("Closing expanded item");
}

// Initialize styles (moved to enhanced function below)

// Animation loop for smooth drag
function animate() {
  currentX += (targetX - currentX) * settings.dragEase;
  currentY += (targetY - currentY) * settings.dragEase;
  canvas.style.transform = `translate(${currentX}px, ${currentY}px)`;
  updateVisibleItems();
  requestAnimationFrame(animate);
}

// Handle navbar scroll behavior for gallery
function handleNavbarScroll() {
  const navbar = document.querySelector(".new-navbar");
  if (navbar) {
    // Hide logo when gallery is being dragged
    const scrollDistance = Math.sqrt(targetX * targetX + targetY * targetY);
    if (scrollDistance > 100) {
      navbar.classList.add("scrolled");
    } else {
      navbar.classList.remove("scrolled");
    }
  }
}

// Clean animation loop
function animateWithNavbar() {
  // Smooth interpolation
  currentX += (targetX - currentX) * settings.dragEase;
  currentY += (targetY - currentY) * settings.dragEase;

  // Use transform3d for hardware acceleration
  canvas.style.transform = `translate3d(${currentX}px, ${currentY}px, 0)`;

  // Update visible items
  updateVisibleItems();

  // Handle navbar scroll behavior
  handleNavbarScroll();

  requestAnimationFrame(animateWithNavbar);
}

// Initialize everything with better error checking
document.addEventListener("DOMContentLoaded", () => {
  console.log("DOM loaded, checking gallery elements...");
  console.log("Container:", container);
  console.log("Canvas:", canvas);
  console.log("Overlay:", overlay);
  console.log("Project title:", projectTitleElement);

  if (container && canvas && overlay && projectTitleElement) {
    console.log("All elements found, initializing gallery...");
    initializeStyles();
    updateVisibleItems();
    animateWithNavbar();
    setTimeout(initTweakpane, 500);

    // Show instructions after a brief delay
    setTimeout(() => {
      showInstructions();
    }, 1000);

    console.log("Gallery page initialized for Diqra Architecture");
  } else {
    console.error("Gallery elements not found!");
    console.error("Missing elements:", {
      container: !container,
      canvas: !canvas,
      overlay: !overlay,
      projectTitleElement: !projectTitleElement,
    });
  }
});

// Mobile-responsive resize handler
function handleResize() {
  // Update settings based on new screen size
  const newIsMobile = isMobile();
  const newIsTablet = isTablet();

  // Update responsive settings (reduced sizes for better performance)
  settings.baseWidth = newIsMobile ? 250 : newIsTablet ? 320 : 360;
  settings.smallHeight = newIsMobile ? 200 : newIsTablet ? 250 : 280;
  settings.largeHeight = newIsMobile ? 320 : newIsTablet ? 380 : 420;
  settings.itemGap = newIsMobile ? 35 : newIsTablet ? 45 : 55;
  settings.hoverScale = newIsMobile ? 1.0 : 1.05;
  settings.expandedScale = newIsMobile ? 0.6 : 0.4;
  settings.dragEase = newIsMobile ? 0.25 : 0.15;
  settings.momentumFactor = newIsMobile ? 150 : 200;
  settings.bufferZone = newIsMobile ? 1 : 2;
  settings.borderRadius = newIsMobile ? 12 : 0;
  settings.vignetteStrength = newIsMobile ? 0.5 : 0.7;
  settings.vignetteSize = newIsMobile ? 100 : 200;
  settings.zoomDuration = newIsMobile ? 0.4 : 0.6;

  // Update columns
  columns = newIsMobile ? 2 : newIsTablet ? 3 : 4;

  // Update item sizes
  itemSizes = [
    {
      width: settings.baseWidth,
      height: settings.smallHeight,
    },
    {
      width: settings.baseWidth,
      height: settings.largeHeight,
    },
  ];

  // Update grid calculations
  itemGap = settings.itemGap;
  cellWidth = settings.baseWidth + settings.itemGap;
  cellHeight =
    Math.max(settings.smallHeight, settings.largeHeight) + settings.itemGap;

  // Clear and regenerate gallery items
  canvas.innerHTML = "";
  visibleItems.clear();
  updateVisibleItems();

  // Update CSS custom properties
  initializeStyles();
}

// Add resize event listener with debouncing
let resizeTimeout;
window.addEventListener("resize", () => {
  clearTimeout(resizeTimeout);
  resizeTimeout = setTimeout(handleResize, 250);
});

// Enhanced initialize styles for gallery page (merged function)
function initializeStyles() {
  // Original functionality
  updateBorderRadius();
  updateVignetteSize();
  updateHoverScale();
  updatePageVignette();

  // Set CSS custom properties for gallery page
  document.documentElement.style.setProperty(
    "--gallery-page-vignette-size",
    `${settings.vignetteSize}px`
  );
  document.documentElement.style.setProperty(
    "--gallery-page-vignette-color",
    `rgba(0, 0, 0, ${settings.vignetteStrength})`
  );

  // Set mobile-specific properties
  if (isMobile()) {
    document.documentElement.style.setProperty(
      "--gallery-border-radius",
      "12px"
    );
    document.documentElement.style.setProperty("--gallery-hover-scale", "1.0");
  } else {
    document.documentElement.style.setProperty(
      "--gallery-border-radius",
      "0px"
    );
    document.documentElement.style.setProperty("--gallery-hover-scale", "1.05");
  }
}

// Also try immediate initialization in case DOM is already loaded
if (document.readyState === "loading") {
  // DOM is still loading, wait for DOMContentLoaded
} else {
  // DOM is already loaded
  console.log("DOM already loaded, initializing immediately...");
  if (container && canvas && overlay && projectTitleElement) {
    initializeStyles();
    updateVisibleItems();
    animateWithNavbar();
    setTimeout(initTweakpane, 500);

    // Show instructions after a brief delay
    setTimeout(() => {
      showInstructions();
    }, 1000);

    console.log("Gallery initialized immediately");
  }
}
