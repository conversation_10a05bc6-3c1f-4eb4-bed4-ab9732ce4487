// <PERSON>rip<PERSON> to add EmailJ<PERSON> to all HTML files
const fs = require('fs');
const path = require('path');

const emailjsScript = `    <!-- EmailJS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>
    
`;

// List of HTML files that need EmailJS
const htmlFiles = [
  'gallery.html',
  'urban-loft.html',
  'corporate-office.html',
  'consultation.html',
  'interior.html',
  'project-management.html',
  'construction.html',
  'renovation.html',
  'testimonials.html',
  'project.html',
  'public/about.html',
  'public/process.html',
  'public/works.html',
  'public/gallery.html',
  'public/modern-villa.html',
  'public/urban-loft.html',
  'public/corporate-office.html',
  'public/consultation.html',
  'public/interior.html',
  'public/project-management.html',
  'public/construction.html',
  'public/renovation.html',
  'public/testimonials.html',
  'public/project.html',
  'public/project-template.html'
];

htmlFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Check if EmailJS is already added
    if (content.includes('@emailjs/browser')) {
      console.log(`EmailJS already exists in ${filePath}`);
      return;
    }
    
    // Find the first script tag and add EmailJS before it
    const scriptRegex = /<script[^>]*src[^>]*>/i;
    const match = content.match(scriptRegex);
    
    if (match) {
      const insertIndex = content.indexOf(match[0]);
      const newContent = content.slice(0, insertIndex) + emailjsScript + content.slice(insertIndex);
      
      fs.writeFileSync(filePath, newContent);
      console.log(`Added EmailJS to ${filePath}`);
    } else {
      console.log(`No script tag found in ${filePath}`);
    }
  } else {
    console.log(`File not found: ${filePath}`);
  }
});

console.log('EmailJS script addition completed!');
