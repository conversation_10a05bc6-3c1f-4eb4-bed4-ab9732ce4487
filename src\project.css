/* Project Page Styles - OH Architecture Inspired */

/* Project Hero Section with Parallax */
.project-hero {
  position: relative;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 120%;
  z-index: -1;
}

.hero-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.7);
}

.project-hero-content {
  text-align: center;
  color: white;
  z-index: 2;
  max-width: 800px;
  padding: 0 2rem;
}

.project-breadcrumb {
  font-size: 1rem;
  margin-bottom: 2rem;
  opacity: 0.8;
}

.project-breadcrumb a {
  color: white;
  text-decoration: none;
  transition: opacity 0.3s ease;
}

.project-breadcrumb a:hover {
  opacity: 0.7;
}

.project-breadcrumb span {
  margin: 0 0.5rem;
}

.project-title {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 2rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.project-meta {
  display: flex;
  justify-content: center;
  gap: 2rem;
  font-size: 1.1rem;
  opacity: 0.9;
}

.project-meta span {
  position: relative;
}

.project-meta span:not(:last-child)::after {
  content: "•";
  position: absolute;
  right: -1rem;
  opacity: 0.6;
}

/* Project Description Section */
.project-description {
  padding: 8rem 0;
  background: #f8f8f8;
}

.project-description .container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 4rem;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 6rem;
  align-items: start;
}

.description-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #111;
  margin-bottom: 2rem;
  line-height: 1.2;
}

.description-content p {
  font-size: 1.2rem;
  line-height: 1.7;
  color: #666;
  margin-bottom: 1.5rem;
}

.project-details {
  background: white;
  padding: 3rem;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.detail-item {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.detail-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.detail-item h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1a3a2a;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item p {
  font-size: 1.1rem;
  color: #333;
  margin: 0;
}

/* Project Gallery */
.project-gallery {
  padding: 0;
}

.gallery-image {
  margin-bottom: 2rem;
  overflow: hidden;
}

.gallery-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.gallery-image:hover img {
  transform: scale(1.02);
}

.full-width {
  height: 70vh;
}

.gallery-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.half-width {
  height: 50vh;
}

.gallery-row.thirds {
  grid-template-columns: 1fr 1fr 1fr;
}

.third-width {
  height: 40vh;
}

/* Project CTA Section */
.project-cta {
  padding: 8rem 0;
  background: #111;
  color: white;
  text-align: center;
}

.project-cta .container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.cta-content h2 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.cta-content p {
  font-size: 1.3rem;
  margin-bottom: 3rem;
  opacity: 0.8;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 2rem;
}

.cta-btn {
  padding: 1.2rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  font-family: inherit;
}

.cta-btn.primary {
  background: #2a4d3b;
  color: white;
}

.cta-btn.primary:hover {
  background: #1a3a2a;
  transform: translateY(-2px);
}

.cta-btn.secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.cta-btn.secondary:hover {
  background: white;
  color: #111;
}

/* Project Footer */
.project-footer {
  background: #111;
  color: white;
  padding: 4rem 0 2rem;
}

.project-footer .container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 4rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 3rem;
}

.footer-logo img {
  height: 3rem;
  margin-bottom: 1.5rem;
}

.footer-left p {
  font-size: 1.1rem;
  opacity: 0.8;
  line-height: 1.6;
}

.footer-nav {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-nav-link {
  color: white;
  text-decoration: none;
  font-size: 1.1rem;
  transition: opacity 0.3s ease;
}

.footer-nav-link:hover {
  opacity: 0.7;
}

.footer-bottom {
  padding-top: 2rem;
  border-top: 1px solid #333;
  text-align: center;
}

.footer-bottom p {
  opacity: 0.6;
  margin: 0;
}

/* Container utility */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 4rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .project-description .container {
    grid-template-columns: 1fr;
    gap: 4rem;
    padding: 0 3rem;
  }

  .project-details {
    padding: 2rem;
  }

  .gallery-row {
    grid-template-columns: 1fr;
  }

  .half-width,
  .third-width {
    height: 50vh;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .container {
    padding: 0 3rem;
  }
}

@media (max-width: 768px) {
  .project-hero-content {
    padding: 0 1.5rem;
  }

  .project-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .project-meta span::after {
    display: none;
  }

  .project-description {
    padding: 6rem 0;
  }

  .project-description .container {
    padding: 0 2rem;
  }

  .description-content h2 {
    font-size: 2rem;
  }

  .description-content p {
    font-size: 1.1rem;
  }

  .project-details {
    padding: 1.5rem;
  }

  .full-width {
    height: 50vh;
  }

  .half-width,
  .third-width {
    height: 40vh;
  }

  .project-cta {
    padding: 6rem 0;
  }

  .cta-content h2 {
    font-size: 2.5rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-btn {
    width: 100%;
    max-width: 300px;
  }

  .container {
    padding: 0 2rem;
  }

  .footer-nav {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .project-hero-content {
    padding: 0 1rem;
  }

  .project-title {
    font-size: clamp(2.5rem, 8vw, 4rem);
  }

  .project-description .container,
  .container {
    padding: 0 1rem;
  }

  .full-width,
  .half-width,
  .third-width {
    height: 35vh;
  }

  .gallery-image {
    margin-bottom: 1rem;
  }

  .gallery-row {
    gap: 1rem;
  }
}

/* Project Card Hover Effects for Works Page */
.project-card {
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.project-card:hover .project-image img {
  transform: scale(1.05);
}

.project-card .project-image {
  overflow: hidden;
}

.project-card .project-image img {
  transition: transform 0.5s ease;
}
