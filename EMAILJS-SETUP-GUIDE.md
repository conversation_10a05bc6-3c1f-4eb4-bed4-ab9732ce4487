# 📧 EmailJS Integration Setup Guide - DIQRA Architects

## ✅ What's Already Done

1. **Updated all contact-sidebar.js files** with EmailJS functionality
2. **Added EmailJS CDN script** to all major HTML files
3. **Implemented loading states** and professional notifications
4. **Added error handling** for failed email sends

## 🚀 Next Steps - Complete Setup

### Step 1: Create EmailJS Account

1. Go to [EmailJS.com](https://www.emailjs.com/)
2. Sign up for a free account
3. Verify your email address

### Step 2: Set Up Email Service

1. **Go to Email Services** in your EmailJS dashboard
2. **Click "Add New Service"**
3. **Choose your email provider:**
   - **Gmail** (recommended for testing)
   - **Outlook/Hotmail**
   - **Yahoo**
   - **Custom SMTP** (for business emails)

4. **For Gmail:**
   - Click "Connect Account"
   - Sign in with your Gmail account
   - Allow EmailJS permissions
   - **Service ID** will be generated (e.g., `service_abc123`)

### Step 3: Create Email Template

1. **Go to Email Templates** in your dashboard
2. **Click "Create New Template"**
3. **Use this template:**

```
Subject: New Inquiry from {{from_name}} - DIQRA Architects

From: {{from_name}}
Email: {{from_email}}
Phone: {{phone}}
Project Type: {{project_type}}

Message:
{{message}}

---
This email was sent from the DIQRA Architects contact form.
```

4. **Template variables to include:**
   - `{{from_name}}`
   - `{{from_email}}`
   - `{{phone}}`
   - `{{project_type}}`
   - `{{message}}`
   - `{{to_email}}`

5. **Save template** - note the **Template ID** (e.g., `template_xyz789`)

### Step 4: Get Your Public Key

1. **Go to Account** in your dashboard
2. **Find "Public Key"** (e.g., `user_abcdefghijk123`)
3. **Copy this key**

### Step 5: Update Configuration

**Replace these placeholders in ALL contact-sidebar.js files:**

```javascript
// EmailJS configuration
const serviceID = 'YOUR_SERVICE_ID'; // Replace with your service ID
const templateID = 'YOUR_TEMPLATE_ID'; // Replace with your template ID  
const publicKey = 'YOUR_PUBLIC_KEY'; // Replace with your public key
```

**With your actual values:**

```javascript
// EmailJS configuration
const serviceID = 'service_abc123'; // Your actual service ID
const templateID = 'template_xyz789'; // Your actual template ID
const publicKey = 'user_abcdefghijk123'; // Your actual public key
```

### Step 6: Update Email Address

**Change the recipient email in all contact-sidebar.js files:**

```javascript
to_email: '<EMAIL>', // Change to your actual email
```

**To your actual business email:**

```javascript
to_email: '<EMAIL>',
```

## 📁 Files That Need Configuration Updates

Update these files with your EmailJS credentials:

- `contact-sidebar.js`
- `public/contact-sidebar.js`
- `src/contact-sidebar.js`
- `assets/contact-sidebar.js`

## 🧪 Testing

1. **Open any page** with a contact form
2. **Fill out the form** with test data
3. **Click "Send Message"**
4. **Check for:**
   - Loading state ("Sending..." button)
   - Success notification
   - Email received in your inbox
   - Form reset and sidebar close

## 🎯 Features Included

- ✅ **Loading states** - Button shows "Sending..." while processing
- ✅ **Success notifications** - Green notification on successful send
- ✅ **Error handling** - Red notification if sending fails
- ✅ **Form validation** - Required fields are validated
- ✅ **Auto-close** - Sidebar closes after successful send
- ✅ **Professional styling** - Animated notifications with proper styling

## 🔧 Troubleshooting

### Common Issues:

1. **"emailjs is not defined"**
   - Make sure EmailJS script is loaded before contact-sidebar.js
   - Check browser console for script loading errors

2. **"Service ID not found"**
   - Verify your service ID is correct
   - Make sure the service is active in EmailJS dashboard

3. **"Template not found"**
   - Check your template ID
   - Ensure template is published

4. **Emails not received**
   - Check spam folder
   - Verify recipient email address
   - Test with a different email address

## 📊 EmailJS Free Tier Limits

- **200 emails/month** - Perfect for most small businesses
- **Upgrade available** for higher volumes
- **No credit card required** for free tier

## 🎉 Ready to Go!

Once you complete the configuration, your contact form will:
1. Capture user inquiries
2. Send professional emails to your inbox
3. Show users confirmation of successful submission
4. Handle errors gracefully

Your contact form is now enterprise-ready! 🚀
