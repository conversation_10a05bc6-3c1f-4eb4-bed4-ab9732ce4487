// New Navbar with 3D Menu Transformation for Diqra Architecture
document.addEventListener("DOMContentLoaded", () => {
  const newContainer = document.querySelector(".new-container");
  const stickyMenuBtn = document.getElementById("sticky-menu-btn");
  const newMenuOverlay = document.querySelector(".new-menu-overlay");
  const newMenuContent = document.querySelector(".new-menu-content");
  const newMenuPreviewImg = document.querySelector(".new-menu-preview-img");
  const newMenuLinks = document.querySelectorAll(".new-link a");
  const contactButton = document.getElementById("contact-button");

  let isOpen = false;
  let isAnimating = false;

  // Handle menu toggle click
  if (stickyMenuBtn) {
    stickyMenuBtn.addEventListener("click", () => {
      if (isOpen) {
        closeMenu();
      } else {
        openMenu();
      }
    });
  }

  function cleanupPreviewImages() {
    const previewImages = newMenuPreviewImg.querySelectorAll("img");
    if (previewImages.length > 3) {
      for (let i = 0; i < previewImages.length - 3; i++) {
        newMenuPreviewImg.removeChild(previewImages[i]);
      }
    }
  }

  function resetPreviewImage() {
    newMenuPreviewImg.innerHTML = "";
    const defaultPreviewImg = document.createElement("img");
    defaultPreviewImg.src =
      "https://cdn.cosmos.so/94579ea4-daee-43f9-b778-84156b731361.jpeg";
    defaultPreviewImg.alt = "Diqra Architecture";
    newMenuPreviewImg.appendChild(defaultPreviewImg);
  }

  function animateMenuButton(isOpening) {
    if (stickyMenuBtn) {
      const span = stickyMenuBtn.querySelector("span");
      if (span) {
        // Check if the animation structure exists (created by animations.js)
        const textOriginal = span.querySelector(".text-original");
        const textHover = span.querySelector(".text-hover");

        if (textOriginal && textHover) {
          // Update both animation text elements
          const newText = isOpening ? "Close" : "Menu";
          textOriginal.textContent = newText;
          textHover.textContent = newText;
        } else {
          // Fallback if animation structure doesn't exist yet
          span.textContent = isOpening ? "Close" : "Menu";
        }
      } else {
        stickyMenuBtn.textContent = isOpening ? "Close" : "Menu";
      }
    }
  }

  function openMenu() {
    if (isAnimating || isOpen) return;
    isAnimating = true;

    // Contact button stays visible as part of navbar

    // 3D transformation of container
    // gsap.to(newContainer, {
    //   rotation: 10,
    //   x: 300,
    //   y: 450,
    //   scale: 1.5,
    //   duration: 1.25,
    //   ease: "power4.inOut",
    // });

    animateMenuButton(true);

    gsap.to(newMenuContent, {
      rotation: 0,
      x: 0,
      y: 0,
      scale: 1,
      opacity: 1,
      duration: 1.25,
      ease: "power4.inOut",
    });

    gsap.to([".new-link a", ".new-social a"], {
      y: "0%",
      opacity: 1,
      duration: 1,
      delay: 0.75,
      stagger: 0.1,
      ease: "power3.out",
    });

    gsap.to(newMenuOverlay, {
      clipPath: "polygon(0% 0%, 100% 0%, 100% 175%, 0% 100%)",
      duration: 1.25,
      ease: "power4.inOut",
      onComplete: () => {
        isOpen = true;
        isAnimating = false;
      },
    });
  }

  function closeMenu() {
    if (isAnimating || !isOpen) return;
    isAnimating = true;

    // Contact button stays visible as part of navbar

    gsap.to(newContainer, {
      rotation: 0,
      x: 0,
      y: 0,
      scale: 1,
      duration: 1.25,
      ease: "power4.inOut",
    });

    animateMenuButton(false);

    gsap.to(newMenuContent, {
      rotation: -15,
      x: -100,
      y: -100,
      scale: 1.5,
      opacity: 0.25,
      duration: 1.25,
      ease: "power4.inOut",
    });

    gsap.to(newMenuOverlay, {
      clipPath: "polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%)",
      duration: 1.25,
      ease: "power4.inOut",
      onComplete: () => {
        isOpen = false;
        isAnimating = false;
        gsap.set([".new-link a", ".new-social a"], { y: "120%" });
        resetPreviewImage();
      },
    });
  }

  // Image preview on hover
  newMenuLinks.forEach((link) => {
    link.addEventListener("mouseover", () => {
      if (!isOpen || isAnimating) return;

      const imgSrc = link.getAttribute("data-img");
      if (!imgSrc) return;

      const previewImages = newMenuPreviewImg.querySelectorAll("img");
      if (
        previewImages.length > 0 &&
        previewImages[previewImages.length - 1].src.includes(imgSrc)
      ) {
        return;
      }

      const newPreviewImg = document.createElement("img");
      newPreviewImg.src = imgSrc;
      newPreviewImg.alt = link.textContent;
      newPreviewImg.style.opacity = "0";
      newPreviewImg.style.transform = "scale(1.25) rotate(10deg)";

      newMenuPreviewImg.appendChild(newPreviewImg);
      cleanupPreviewImages();

      gsap.to(newPreviewImg, {
        opacity: 1,
        scale: 1,
        rotation: 0,
        duration: 0.75,
        ease: "power2.out",
      });
    });
  });

  // Handle navigation clicks
  newMenuLinks.forEach((link) => {
    link.addEventListener("click", (e) => {
      const href = link.getAttribute("href");

      // Handle anchor links
      if (href.startsWith("#")) {
        e.preventDefault();
        closeMenu();

        setTimeout(() => {
          const targetSection = document.querySelector(href);
          if (targetSection) {
            targetSection.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        }, 500);
      } else {
        // For regular page links, close the menu and let the navigation happen
        closeMenu();
      }
    });
  });

  // Handle footer links and social links
  const footerLinks = document.querySelectorAll(".new-menu-footer a");
  const socialLinks = document.querySelectorAll(".new-menu-socials a");

  [...footerLinks, ...socialLinks].forEach((link) => {
    link.addEventListener("click", (e) => {
      const href = link.getAttribute("href");

      if (href.startsWith("#")) {
        e.preventDefault();
        closeMenu();

        setTimeout(() => {
          const targetSection = document.querySelector(href);
          if (targetSection) {
            targetSection.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        }, 500);
      } else {
        // For regular page links, close the menu and let the navigation happen
        closeMenu();
      }
    });
  });

  // Initial states for animations
  gsap.set([".new-link a", ".new-social a"], {
    transform: "translateY(120%)",
    opacity: 0.25,
  });
  gsap.set(newMenuContent, {
    transform:
      "translateX(-100px) translateY(-100px) scale(1.5) rotate(-15deg)",
    opacity: 0.25,
  });
  gsap.set(newMenuOverlay, {
    clipPath: "polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%)",
  });

  // Handle scroll-based logo visibility
  const navbar = document.querySelector(".new-navbar");
  let lastScrollY = window.scrollY;

  function handleScroll() {
    const currentScrollY = window.scrollY;

    // Hide logo when scrolled down from hero section (after 100vh)
    if (currentScrollY > window.innerHeight * 0.8) {
      navbar.classList.add("scrolled");
    } else {
      navbar.classList.remove("scrolled");
    }

    lastScrollY = currentScrollY;
  }

  // Add scroll event listener
  window.addEventListener("scroll", handleScroll);

  // Initial check
  handleScroll();

  console.log("New navbar initialized for Diqra Architecture");
});
