// Project Data (embedded to avoid CORS issues)
const PROJECT_DATA = {
  "modern-villa": {
    "id": "modern-villa",
    "title": "Modern Villa",
    "category": "Residential",
    "year": "2024",
    "location": "Brisbane, Australia",
    "description": "A contemporary residential project that seamlessly blends modern architecture with natural surroundings. This villa showcases clean lines, expansive glass facades, and sustainable design principles.",
    "heroImage": "https://cdn.cosmos.so/94579ea4-daee-43f9-b778-84156b731361.jpeg",
    "details": {
      "client": "Private Residence",
      "area": "450 sqm",
      "status": "Completed",
      "architect": "DIQRA Architects"
    },
    "gallery": [
      {
        "image": "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=2075&q=80",
        "type": "full-width",
        "alt": "Modern Villa Exterior"
      },
      {
        "image": "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=2075&q=80",

        "type": "half-width",
        "alt": "Living Room"
      },
      {
        "image": "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=2075&q=80",

        "type": "half-width",
        "alt": "Kitchen"
      },
      {
        "image": "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=2075&q=80",

        "type": "full-width",
        "alt": "Bedroom"
      }
    ]
  },
  "urban-loft": {
    "id": "urban-loft",
    "title": "Urban Loft",
    "category": "Residential",
    "year": "2023",
    "location": "Melbourne, Australia",
    "description": "An industrial loft conversion that transforms a former warehouse into a sophisticated urban dwelling. The design celebrates the building's heritage while introducing contemporary living spaces.",
    "heroImage": "https://cdn.cosmos.so/5f8d5539-943c-4df5-bae8-8e714633ddd0.jpeg",
    "details": {
      "client": "Urban Developer",
      "area": "320 sqm",
      "status": "Completed",
      "architect": "DIQRA Architects"
    },
    "gallery": [
      {
        "image": "https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "full-width",
        "alt": "Urban Loft Interior"
      },
      {
        "image": "https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "half-width",
        "alt": "Open Plan Living"
      },
      {
        "image": "https://images.unsplash.com/photo-1600607688969-a5bfcd646154?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "half-width",
        "alt": "Industrial Details"
      }
    ]
  },
  "corporate-office": {
    "id": "corporate-office",
    "title": "Corporate Office",
    "category": "Commercial",
    "year": "2024",
    "location": "Sydney, Australia",
    "description": "A modern corporate headquarters designed to foster collaboration and innovation. The space features flexible work areas, natural lighting, and sustainable materials throughout.",
    "heroImage": "https://cdn.cosmos.so/f733585a-081e-48e7-a30e-e636446f2168.jpeg",
    "details": {
      "client": "Tech Corporation",
      "area": "1200 sqm",
      "status": "Completed",
      "architect": "DIQRA Architects"
    },
    "gallery": [
      {
        "image": "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80",
        "type": "full-width",
        "alt": "Office Reception"
      },
      {
        "image": "https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80",
        "type": "half-width",
        "alt": "Meeting Room"
      },
      {
        "image": "https://images.unsplash.com/photo-1497366412874-3415097a27e7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80",
        "type": "half-width",
        "alt": "Open Office"
      }
    ]
  },
  "luxury-penthouse": {
    "id": "luxury-penthouse",
    "title": "Luxury Penthouse",
    "category": "Residential",
    "year": "2024",
    "location": "Gold Coast, Australia",
    "description": "An exclusive penthouse design that maximizes panoramic ocean views while creating intimate living spaces. The design features premium materials and cutting-edge smart home technology.",
    "heroImage": "https://cdn.cosmos.so/ce9f9fd7-a2a5-476d-9757-481ca01b5861.jpeg",
    "featured": true,
    "details": {
      "client": "Private Client",
      "area": "380 sqm",
      "status": "Completed",
      "architect": "DIQRA Architects"
    },
    "gallery": [
      {
        "image": "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2053&q=80",
        "type": "full-width",
        "alt": "Penthouse Living Room"
      },
      {
        "image": "https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "half-width",
        "alt": "Ocean View Terrace"
      },
      {
        "image": "https://images.unsplash.com/photo-1600607687644-aac4c3eac7f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2053&q=80",
        "type": "half-width",
        "alt": "Master Suite"
      }
    ]
  },
  "sustainable-house": {
    "id": "sustainable-house",
    "title": "Sustainable House",
    "category": "Residential",
    "year": "2023",
    "location": "Adelaide, Australia",
    "description": "An eco-friendly family home that demonstrates sustainable architecture principles. Features solar panels, rainwater harvesting, natural ventilation, and locally sourced materials.",
    "heroImage": "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    "details": {
      "client": "Eco-Conscious Family",
      "area": "280 sqm",
      "status": "Completed",
      "architect": "DIQRA Architects"
    },
    "gallery": [
      {
        "image": "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "full-width",
        "alt": "Sustainable House Exterior"
      },
      {
        "image": "https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "half-width",
        "alt": "Solar Panel Integration"
      },
      {
        "image": "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2053&q=80",
        "type": "half-width",
        "alt": "Natural Living Space"
      }
    ]
  },
  "boutique-hotel": {
    "id": "boutique-hotel",
    "title": "Boutique Hotel",
    "category": "Hospitality",
    "year": "2024",
    "location": "Byron Bay, Australia",
    "description": "A luxury boutique hotel that blends contemporary design with coastal charm. Each suite offers unique architectural features and stunning ocean or hinterland views.",
    "heroImage": "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    "details": {
      "client": "Hospitality Group",
      "area": "2500 sqm",
      "status": "Completed",
      "architect": "DIQRA Architects"
    },
    "gallery": [
      {
        "image": "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "full-width",
        "alt": "Hotel Lobby"
      },
      {
        "image": "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "half-width",
        "alt": "Luxury Suite"
      },
      {
        "image": "https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "half-width",
        "alt": "Ocean View Terrace"
      }
    ]
  },
  "art-gallery": {
    "id": "art-gallery",
    "title": "Contemporary Art Gallery",
    "category": "Cultural",
    "year": "2023",
    "location": "Perth, Australia",
    "description": "A minimalist art gallery designed to showcase contemporary works. The space features flexible exhibition areas, controlled lighting, and a seamless flow between indoor and outdoor spaces.",
    "heroImage": "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    "details": {
      "client": "Arts Foundation",
      "area": "800 sqm",
      "status": "Completed",
      "architect": "DIQRA Architects"
    },
    "gallery": [
      {
        "image": "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "full-width",
        "alt": "Gallery Interior"
      },
      {
        "image": "https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "half-width",
        "alt": "Exhibition Space"
      },
      {
        "image": "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "half-width",
        "alt": "Sculpture Court"
      }
    ]
  },
  "medical-center": {
    "id": "medical-center",
    "title": "Medical Center",
    "category": "Healthcare",
    "year": "2024",
    "location": "Canberra, Australia",
    "description": "A state-of-the-art medical facility designed with patient comfort and operational efficiency in mind. Features healing gardens, natural lighting, and flexible treatment spaces.",
    "heroImage": "https://images.unsplash.com/photo-1586773860418-d37222d8fce3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2073&q=80",
    "details": {
      "client": "Healthcare Provider",
      "area": "1500 sqm",
      "status": "Completed",
      "architect": "DIQRA Architects"
    },
    "gallery": [
      {
        "image": "https://images.unsplash.com/photo-1586773860418-d37222d8fce3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2073&q=80",
        "type": "full-width",
        "alt": "Medical Center Entrance"
      },
      {
        "image": "https://images.unsplash.com/photo-**********-2a8555f1a136?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "half-width",
        "alt": "Waiting Area"
      },
      {
        "image": "https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "half-width",
        "alt": "Healing Garden"
      }
    ]
  },
  "warehouse-conversion": {
    "id": "warehouse-conversion",
    "title": "Warehouse Conversion",
    "category": "Mixed-Use",
    "year": "2023",
    "location": "Newcastle, Australia",
    "description": "A creative conversion of an industrial warehouse into mixed-use spaces including artist studios, retail, and residential lofts. Preserves the building's industrial character while adding modern amenities.",
    "heroImage": "https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    "details": {
      "client": "Property Developer",
      "area": "3200 sqm",
      "status": "Completed",
      "architect": "DIQRA Architects"
    },
    "gallery": [
      {
        "image": "https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "full-width",
        "alt": "Warehouse Interior"
      },
      {
        "image": "https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "half-width",
        "alt": "Artist Studio"
      },
      {
        "image": "https://images.unsplash.com/photo-1600607688969-a5bfcd646154?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "half-width",
        "alt": "Retail Space"
      }
    ]
  },
  "school-extension": {
    "id": "school-extension",
    "title": "School Extension",
    "category": "Educational",
    "year": "2024",
    "location": "Hobart, Australia",
    "description": "A modern extension to a heritage school building that provides new learning spaces while respecting the original architecture. Features flexible classrooms, collaborative areas, and sustainable design elements.",
    "heroImage": "https://images.unsplash.com/photo-1580582932707-520aed937b7b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2032&q=80",
    "details": {
      "client": "Department of Education",
      "area": "900 sqm",
      "status": "Completed",
      "architect": "DIQRA Architects"
    },
    "gallery": [
      {
        "image": "https://images.unsplash.com/photo-1580582932707-520aed937b7b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2032&q=80",
        "type": "full-width",
        "alt": "School Extension Exterior"
      },
      {
        "image": "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80",
        "type": "half-width",
        "alt": "Modern Classroom"
      },
      {
        "image": "https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80",
        "type": "half-width",
        "alt": "Collaborative Learning Space"
      }
    ]
  },
  "coastal-retreat": {
    "id": "coastal-retreat",
    "title": "Coastal Retreat",
    "category": "Residential",
    "year": "2023",
    "location": "Sunshine Coast, Australia",
    "description": "A serene coastal retreat designed to maximize ocean views and natural ventilation. The design features elevated living spaces, extensive use of timber, and seamless indoor-outdoor living.",
    "heroImage": "https://images.unsplash.com/photo-1600585154526-990dced4db0d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    "details": {
      "client": "Private Retreat",
      "area": "350 sqm",
      "status": "Completed",
      "architect": "DIQRA Architects"
    },
    "gallery": [
      {
        "image": "https://images.unsplash.com/photo-1600585154526-990dced4db0d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "full-width",
        "alt": "Coastal Retreat Exterior"
      },
      {
        "image": "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2053&q=80",
        "type": "half-width",
        "alt": "Ocean View Living"
      },
      {
        "image": "https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        "type": "half-width",
        "alt": "Timber Deck"
      }
    ]
  },
  "innovation-hub": {
    "id": "innovation-hub",
    "title": "Innovation Hub",
    "category": "Commercial",
    "year": "2024",
    "location": "Darwin, Australia",
    "description": "A cutting-edge innovation center designed to foster creativity and collaboration. Features flexible co-working spaces, maker labs, presentation areas, and biophilic design elements.",
    "heroImage": "https://images.unsplash.com/photo-1497366412874-3415097a27e7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80",
    "details": {
      "client": "Tech Incubator",
      "area": "1800 sqm",
      "status": "In Progress",
      "architect": "DIQRA Architects"
    },
    "gallery": [
      {
        "image": "https://images.unsplash.com/photo-1497366412874-3415097a27e7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80",
        "type": "full-width",
        "alt": "Innovation Hub Interior"
      },
      {
        "image": "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80",
        "type": "half-width",
        "alt": "Co-working Space"
      },
      {
        "image": "https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80",
        "type": "half-width",
        "alt": "Maker Lab"
      }
    ]
  }
};

// Dynamic Project Page System
class ProjectManager {
  constructor() {
    this.projects = PROJECT_DATA;
    this.currentProject = null;
    this.init();
  }

  init() {
    try {
      // Get project ID from URL parameter
      const projectId = this.getProjectIdFromURL();

      if (projectId && this.projects[projectId]) {
        this.loadProject(projectId);
      } else {
        this.showError('Project not found');
      }

      // Initialize animations after content is loaded
      setTimeout(() => {
        this.initAnimations();
      }, 100);
    } catch (error) {
      console.error('Error initializing project:', error);
      this.showError('Failed to load project');
    }
  }

  getProjectIdFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('project');
  }

  loadProject(projectId) {
    const project = this.projects[projectId];
    if (!project) {
      this.showError('Project not found');
      return;
    }

    this.currentProject = project;

    // Update page title and meta
    document.getElementById('page-title').textContent = `${project.title} - Diqra Architects`;
    document.getElementById('page-description').content = project.description;

    // Update hero section
    document.getElementById('hero-image').src = project.heroImage;
    document.getElementById('hero-image').alt = project.title;
    document.getElementById('breadcrumb-title').textContent = project.title;
    document.getElementById('project-title').textContent = project.title;

    // Update meta information
    document.getElementById('project-category').textContent = project.category;
    document.getElementById('project-year').textContent = project.year;
    document.getElementById('project-location').textContent = project.location;

    // Update description
    document.getElementById('project-description').textContent = project.description;

    // Update project details
    this.loadProjectDetails(project.details);

    // Load gallery
    this.loadGallery(project.gallery);
  }

  loadProjectDetails(details) {
    const detailsContainer = document.getElementById('project-details');
    detailsContainer.innerHTML = '';

    Object.entries(details).forEach(([key, value]) => {
      const detailItem = document.createElement('div');
      detailItem.className = 'detail-item';
      detailItem.innerHTML = `
        <h4>${this.formatDetailKey(key)}</h4>
        <p>${value}</p>
      `;
      detailsContainer.appendChild(detailItem);
    });
  }

  formatDetailKey(key) {
    return key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');
  }

  loadGallery(gallery) {
    const galleryContainer = document.getElementById('project-gallery');
    galleryContainer.innerHTML = '';

    let i = 0;
    while (i < gallery.length) {
      const item = gallery[i];

      if (item.type === 'full-width') {
        // Create full-width image
        const fullWidthDiv = document.createElement('div');
        fullWidthDiv.className = 'gallery-image full-width';
        const img = this.createImageElement(item);
        fullWidthDiv.appendChild(img);
        galleryContainer.appendChild(fullWidthDiv);
        i++;
      } else if (item.type === 'half-width') {
        // Create row for half-width images
        const rowDiv = document.createElement('div');
        rowDiv.className = 'gallery-row';

        // Add first half-width image
        const img1Div = document.createElement('div');
        img1Div.className = 'gallery-image half-width';
        const img1 = this.createImageElement(item);
        img1Div.appendChild(img1);
        rowDiv.appendChild(img1Div);

        // Check if there's a next half-width image
        if (i + 1 < gallery.length && gallery[i + 1].type === 'half-width') {
          const nextItem = gallery[i + 1];
          const img2Div = document.createElement('div');
          img2Div.className = 'gallery-image half-width';
          const img2 = this.createImageElement(nextItem);
          img2Div.appendChild(img2);
          rowDiv.appendChild(img2Div);
          i += 2; // Skip next item since we processed it
        } else {
          i++; // Only one half-width image
        }

        galleryContainer.appendChild(rowDiv);
      } else {
        // Default case
        const defaultDiv = document.createElement('div');
        defaultDiv.className = 'gallery-image';
        const img = this.createImageElement(item);
        defaultDiv.appendChild(img);
        galleryContainer.appendChild(defaultDiv);
        i++;
      }
    }
  }

  createImageElement(item) {
    const img = document.createElement('img');
    img.src = item.image;
    img.alt = item.alt;
    img.loading = 'lazy';
    return img;
  }

  showError(message) {
    document.getElementById('project-title').textContent = 'Error';
    document.getElementById('project-description').textContent = message;
    document.getElementById('project-meta').style.display = 'none';
  }

  initAnimations() {
    // Register GSAP plugins
    gsap.registerPlugin(ScrollTrigger);

    // Hero content animation
    gsap.fromTo('.project-hero-content',
      {
        opacity: 0,
        y: 50
      },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power2.out",
        delay: 0.5
      }
    );

    // Description section animation
    gsap.fromTo('.description-content, .project-details',
      {
        opacity: 0,
        y: 30
      },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out",
        stagger: 0.2,
        scrollTrigger: {
          trigger: ".project-description",
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );

    // Gallery animations
    gsap.fromTo('.gallery-image',
      {
        opacity: 0,
        scale: 0.95
      },
      {
        opacity: 1,
        scale: 1,
        duration: 0.8,
        ease: "power2.out",
        stagger: 0.1,
        scrollTrigger: {
          trigger: ".project-gallery",
          start: "top 85%",
          end: "bottom 15%",
          toggleActions: "play none none reverse"
        }
      }
    );

    // CTA animation
    gsap.fromTo('.cta-content',
      {
        opacity: 0,
        y: 50
      },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
          trigger: ".cta-section",
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );

    // Footer animation
    gsap.fromTo('.footer',
      {
        opacity: 0,
        y: 30
      },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out",
        scrollTrigger: {
          trigger: ".footer",
          start: "top 85%",
          end: "bottom 15%",
          toggleActions: "play none none reverse"
        }
      }
    );
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new ProjectManager();
});

// Export for potential use in other scripts
window.ProjectManager = ProjectManager;
