// Project Page JavaScript - Clean and Simple
document.addEventListener("DOMContentLoaded", () => {
  console.log("Project page initialized - no animations");

  // Ensure all content is visible immediately
  const allElements = document.querySelectorAll(
    ".project-hero-content, .description-content h2, .description-content p, .project-details, .gallery-image, .cta-content, .footer"
  );

  allElements.forEach((element) => {
    element.style.opacity = "1";
    element.style.transform = "none";
  });

  // Ensure images load properly
  const images = document.querySelectorAll("img");
  images.forEach((img) => {
    img.style.opacity = "1";
    img.style.transform = "none";
  });

  // Smooth scroll for internal links
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute("href"));
      if (target) {
        target.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    });
  });

  // Simple hover effects for gallery images (minimal)
  const galleryImages = document.querySelectorAll(".gallery-image");
  galleryImages.forEach((image) => {
    const img = image.querySelector("img");
    if (img) {
      image.addEventListener("mouseenter", () => {
        img.style.transform = "scale(1.02)";
        img.style.transition = "transform 0.3s ease";
      });

      image.addEventListener("mouseleave", () => {
        img.style.transform = "scale(1)";
      });
    }
  });

  console.log("Project page initialized - clean and simple");
});
