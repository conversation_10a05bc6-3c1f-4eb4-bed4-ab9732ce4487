/* Works Page Styles */

/* Page Setup */

.works-page {
  background: #fcfcfc !important;
  color: #111 !important;
  overflow-y: hidden !important;
  overflow-x: auto !important;
  font-family: "Inter", "PP Neue Montreal", sans-serif !important;
  margin: 0 !important;
  padding: 0 !important;
  min-height: 100vh !important;
  width: 100vw !important;
}

/* Force html and body for horizontal scroll */
.works-page html,
.works-page body {
  margin: 0 !important;
  padding: 0 !important;
  overflow-y: hidden !important;
  overflow-x: auto !important;
}

/* Hide footer on works page */
.works-page .footer {
  display: none !important;
}

/* Reset any conflicting styles */
.works-page * {
  box-sizing: border-box;
}

/* Force visibility for works page */
.works-page,
.works-page .new-container,
.works-page .main-content,
.works-page .works-container {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.works-page .works-scroll {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.works-page .project-card {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Override any preloader hiding */
body.works-page .main-content {
  display: block !important;
  opacity: 1 !important;
  transform: translateY(0) !important;
}

body.preloader-active.works-page .main-content {
  display: block !important;
}

/* Works Title - Absolute Positioned */
.works-title {
  position: fixed;
  top: 89%;
  left: 5rem;
  transform: translateY(-50%);
  z-index: 100;
  pointer-events: none;
}

.works-title h1 {
  font-size: clamp(3rem, 7vw, 10rem) !important;
  font-weight: 700;
  color: #111 !important;
  margin: 0;
  line-height: 0.9;
  /* letter-spacing: -0.05em; */
  /* writing-mode: vertical-rl; */
  text-orientation: mixed;
}

/* Scroll Progress Tracker */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  z-index: 1000;
}

.progress-bar {
  height: 100%;
  background: #111;
  width: 10%;
  transition: width 0.1s ease;
}

/* Override main style.css for works page */
.works-page .new-container {
  width: 100vw !important;
  min-height: 100vh !important;
  position: relative !important;
  background: #fcfcfc !important;
  display: block !important;
  opacity: 1 !important;
  transform: none !important;
  overflow-y: hidden !important;
  overflow-x: visible !important;
}

/* Main Content - Override hidden styles */
.works-page .main-content {
  width: 100vw !important;
  min-height: 100vh !important;
  position: relative !important;
  background: #fcfcfc !important;
  display: block !important;
  opacity: 1 !important;
  transform: translateY(0) !important;
  overflow-y: hidden !important;
  overflow-x: visible !important;
}

/* Works Container */
.works-container {
  width: 100vw !important;
  height: 100vh !important;
  overflow-x: auto !important;
  overflow-y: hidden !important;
  position: relative !important;
  display: block !important;
  -webkit-overflow-scrolling: touch !important;
}

/* Hide scrollbar but keep functionality */
.works-container::-webkit-scrollbar {
  display: none;
}

.works-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Horizontal Scroll Content */
.works-scroll {
  display: flex !important;
  flex-direction: row !important;
  height: 100vh !important;
  align-items: center !important;
  padding-left: 5rem !important;
  padding-right: 5rem !important;
  gap: 4rem !important;
  /* Width will be set dynamically by JavaScript to prevent extra white space */
}

/* Project Cards */
.project-card {
  flex-shrink: 0 !important;
  flex-grow: 0 !important;
  width: 40vw !important;
  height: 60vh !important;
  position: relative !important;
  cursor: pointer !important;
  transition: transform 0.3s ease !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  background: #333 !important;
  /* border: 2px solid #555 !important; */
  display: block !important;
}

.project-card:hover {
  transform: scale(1.02);
}

.project-image {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.project-card:hover .project-image img {
  transform: scale(1.05);
}

/* Project Info Overlay */
.project-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 4rem 3rem 3rem;
  transform: translateY(0);
}

.project-info h3 {
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #fff;
}

.project-info p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 300;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .works-title h1 {
    font-size: 10rem;
  }

  .project-card {
    width: 70vw;
    height: 75vh;
  }

  .works-scroll {
    gap: 3rem;
  }
}

@media (max-width: 900px) {
  .works-title {
    left: 2rem;
  }

  .works-title h1 {
    font-size: 8rem;
  }

  .project-card {
    width: 80vw;
    height: 70vh;
  }

  .works-scroll {
    padding: 0 2rem;
    gap: 2rem;
  }

  .project-info {
    padding: 3rem 2rem 2rem;
  }

  .project-info h3 {
    font-size: 2rem;
  }

  .project-info p {
    font-size: 1rem;
  }
}

/* Mobile Responsive - Switch to Vertical Scroll */
@media (max-width: 768px) {
  /* Enable vertical scroll for mobile */
  .works-page {
    overflow-y: auto !important;
    overflow-x: hidden !important;
  }

  .works-page html,
  .works-page body {
    overflow-y: auto !important;
    overflow-x: hidden !important;
  }

  /* Show footer on mobile */
  .works-page .footer {
    display: block !important;
  }

  /* Works container - vertical layout */
  .works-container {
    width: 100vw !important;
    height: auto !important;
    overflow-x: hidden !important;
    overflow-y: visible !important;
    position: relative !important;
    padding-top: 50rem !important; /* Space for title */
  }

  /* Works title - top center position */
  .works-title {
    position: fixed !important;
    top: 22rem !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    text-align: center !important;
    z-index: 10 !important;
    background: rgba(252, 252, 252, 0.95) !important;
    padding: 1rem 2rem !important;
    border-radius: 8px !important;
    backdrop-filter: blur(10px) !important;
  }

  .works-title h1 {
    font-size: 44px !important;
    line-height: 1.1 !important;
    margin: 0 !important;
  }

  /* Works scroll - vertical layout */
  .works-scroll {
    display: flex !important;
    flex-direction: column !important;
    height: auto !important;
    width: 100% !important;
    align-items: center !important;
    padding: 1rem !important;
    gap: 1.5rem !important;
  }

  /* Project cards - smaller mobile layout */
  .project-card {
    width: 90vw !important;
    max-width: 350px !important;
    height: 35vh !important;
    min-height: 250px !important;
    flex-shrink: 0 !important;
    border-radius: 8px !important;
  }

  /* Project info - always visible with better positioning */
  .project-info {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.85)) !important;
    padding: 1.5rem 1rem 1rem !important;
    transform: translateY(0) !important;
  }

  .project-info h3 {
    font-size: 1.2rem !important;
    margin-bottom: 0.3rem !important;
  }

  .project-info p {
    font-size: 0.8rem !important;
    margin: 0 !important;
  }

  /* Hide horizontal scroll progress bar on mobile */
  .scroll-progress {
    display: none !important;
  }

  /* Mobile footer fixes */
  .footer {
    margin-top: 3rem !important;
    padding: 3rem 1rem 2rem !important;
  }

  .footer-container {
    grid-template-columns: 1fr !important;
    gap: 2rem !important;
    text-align: center !important;
  }

  .footer-left {
    order: 2 !important;
  }

  .footer-center {
    order: 1 !important;
    margin-left: 0 !important;
  }

  .footer-right {
    order: 3 !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
  }

  .footer-logo-image1 {
    height: 200px !important;
    width: 200px !important;
    margin: 0 auto 1rem !important;
  }

  .footer-logo-image2 {
    width: 120px !important;
  }

  .footer-nav {
    flex-direction: column !important;
    gap: 1rem !important;
    align-items: center !important;
  }

  .footer-nav-link {
    font-size: 1.5rem !important;
  }

  .footer-acknowledgement h1 {
    font-size: 0.9rem !important;
  }

  .footer-acknowledgement p {
    font-size: 0.8rem !important;
  }

  .footer-address h4 {
    font-size: 0.9rem !important;
  }

  .footer-address p {
    font-size: 0.8rem !important;
  }
}

@media (max-width: 600px) {
  .works-title h1 {
    font-size: 2rem !important;
  }

  .works-title {
    padding: 0.8rem 1.5rem !important;
  }

  .project-card {
    width: 95vw !important;
    height: 30vh !important;
    min-height: 220px !important;
  }

  .project-info {
    padding: 1rem 0.8rem 0.8rem !important;
  }

  .project-info h3 {
    font-size: 1rem !important;
    margin-bottom: 0.2rem !important;
  }

  .project-info p {
    font-size: 0.7rem !important;
  }

  .footer {
    padding: 2rem 0.5rem 1.5rem !important;
  }

  .footer-nav-link {
    font-size: 1.2rem !important;
  }

  .footer-logo-image1 {
    height: 150px !important;
    width: 150px !important;
  }

  .footer-logo-image2 {
    width: 100px !important;
  }
}

/* Loading Animation */
.project-card {
  opacity: 1;
  transform: translateX(0);
  /* Temporarily disable animation for debugging */
  /* animation: fadeInSlide 0.6s ease forwards; */
}

.project-card:nth-child(1) {
  animation-delay: 0.1s;
}
.project-card:nth-child(2) {
  animation-delay: 0.2s;
}
.project-card:nth-child(3) {
  animation-delay: 0.3s;
}
.project-card:nth-child(4) {
  animation-delay: 0.4s;
}
.project-card:nth-child(5) {
  animation-delay: 0.5s;
}
.project-card:nth-child(6) {
  animation-delay: 0.6s;
}
.project-card:nth-child(7) {
  animation-delay: 0.7s;
}
.project-card:nth-child(8) {
  animation-delay: 0.8s;
}
.project-card:nth-child(9) {
  animation-delay: 0.9s;
}
.project-card:nth-child(10) {
  animation-delay: 1s;
}

@keyframes fadeInSlide {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Removed smooth scrolling for better performance */

/* Custom cursor for project cards */
.project-card::after {
  content: "View Project";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #080807;
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.project-card:hover::after {
  opacity: 1;
}
