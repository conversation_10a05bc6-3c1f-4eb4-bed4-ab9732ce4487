gsap.registerPlugin(ScrollTrigger);

// Parallax effect for the parallax image
gsap.to(".parallax-image img", {
  y: 100,
  ease: "none",
  scrollTrigger: {
    trigger: ".parallax-container",
    start: "top bottom",
    end: "bottom top",
    scrub: true,
  },
});

// Sticky image logic
const imageIds = [1, 2, 3, 4, 5, 6];
const images = imageIds.map((num) =>
  document.getElementById(`pin-image-${num}`)
);

// Hide all images except the first
images.forEach((img, i) => img.classList.toggle("active", i === 0));

// Animate image on pin-content scroll
imageIds.forEach((num) => {
  ScrollTrigger.create({
    trigger: `#pin-content-${num}`,
    start: "top center",
    end: "bottom center",
    onEnter: () => setActiveImage(num),
    onEnterBack: () => setActiveImage(num),
  });
});

function setActiveImage(num) {
  images.forEach((img, i) => {
    img.classList.toggle("active", img.id === `pin-image-${num}`);
  });
}
