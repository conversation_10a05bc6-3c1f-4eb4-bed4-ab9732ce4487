// Testimonials Page JavaScript
document.addEventListener("DOMContentLoaded", () => {
  // Testimonials data
  const testimonials = [
    {
      text: "Working with DIQRA Architects was an absolute pleasure. They listened to our needs and created a design that exceeded our expectations. The attention to detail and professionalism throughout the project was outstanding.",
      author: "<PERSON>",
      project: "Contemporary Villa, Gold Coast",
      avatar:
        "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
      rating: 5,
    },
    {
      text: "The team at DIQRA transformed our outdated office space into a modern, functional workspace that our employees love. Their innovative approach and commitment to sustainability impressed us greatly.",
      author: "<PERSON>",
      project: "Corporate Office Renovation",
      avatar:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
      rating: 5,
    },
    {
      text: "From concept to completion, DIQRA Architects delivered exceptional service. They managed every aspect of our home renovation with precision and care. We couldn't be happier with the result.",
      author: "<PERSON> <PERSON> <PERSON>",
      project: "Heritage Home Restoration",
      avatar:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
      rating: 5,
    },
    {
      text: "The design process was collaborative and inspiring. DIQRA's team brought fresh ideas while respecting our vision. The final result is a beautiful space that perfectly suits our lifestyle.",
      author: "Michael Chang",
      project: "Modern Family Home",
      avatar:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
      rating: 5,
    },
    {
      text: "DIQRA Architects delivered our commercial project on time and within budget. Their expertise in sustainable design helped us achieve our environmental goals while creating an impressive space.",
      author: "Sarah Mitchell",
      project: "Eco-Friendly Retail Space",
      avatar:
        "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face",
      rating: 5,
    },
    {
      text: "The level of creativity and technical expertise shown by DIQRA was remarkable. They solved complex design challenges with elegant solutions that enhanced both form and function.",
      author: "Robert Taylor",
      project: "Luxury Apartment Complex",
      avatar:
        "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face",
      rating: 5,
    },
  ];

  // Function to create testimonial card
  function createTestimonialCard(testimonial) {
    const card = document.createElement("div");
    card.className = "testimonial-card";

    const stars = "★".repeat(testimonial.rating);

    card.innerHTML = `
      <div class="testimonial-text">
        "${testimonial.text}"
      </div>
      <div class="testimonial-author">
        <div class="testimonial-avatar">
          <img src="${testimonial.avatar}" alt="${testimonial.author}" />
        </div>
        <div class="testimonial-info">
          <h4>${testimonial.author}</h4>
          <p>${testimonial.project}</p>
          <div class="testimonial-rating">
            ${stars
              .split("")
              .map((star) => `<span class="star">${star}</span>`)
              .join("")}
          </div>
        </div>
      </div>
    `;

    return card;
  }

  // Render testimonials
  function renderTestimonials() {
    const container = document.querySelector(".testimonials-container");
    if (!container) return;

    testimonials.forEach((testimonial, index) => {
      const card = createTestimonialCard(testimonial);
      container.appendChild(card);
    });
  }

  // Initialize animations (simplified - no scroll triggers)
  function initAnimations() {
    // Simple fade in for hero elements without scroll triggers
    gsap.set([".hero-title", ".hero-subtitle", ".stat-item"], { opacity: 1 });
    gsap.set([".featured-quote", ".featured-project", ".grid-header"], {
      opacity: 1,
    });
  }

  // Simple counter display (no animation)
  function displayCounters() {
    const counters = document.querySelectorAll(".stat-number");
    // Just ensure the numbers are displayed correctly
    counters.forEach((counter) => {
      const target = counter.textContent;
      counter.textContent = target; // Keep original values
    });
  }

  // Initialize everything
  renderTestimonials();
  initAnimations();
  displayCounters();

  // Simple hover effects using CSS (no GSAP)
  // Hover effects are now handled by CSS :hover pseudo-class
});
