/* Gallery Page Styles - Namespaced to avoid conflicts */

@import url("https://fonts.cdnfonts.com/css/thegoodmonolith");

@font-face {
  src: url("https://fonts.cdnfonts.com/css/pp-neue-montreal") format("woff2");
  font-family: "PP Neue Montreal", sans-serif;
  font-weight: 400;
}

/* Gallery Page Root Variables */
.gallery-page {
  --gallery-spacing-base: 1rem;
  --gallery-spacing-md: 1.5rem;
  --gallery-spacing-lg: 2rem;
  --gallery-color-text: #ffffff;
  --gallery-color-text-dim: 0.6;
  --gallery-transition-medium: 0.3s ease;
  --gallery-font-size-base: 14px;
}

/* Gallery Page Body Styles */
.gallery-page {
  font-family: "PP Neue Montreal", sans-serif !important;
  background-color: #000000 !important;
  color: #ffffff !important;
  overflow: hidden !important;
  position: relative !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* Override main content display for gallery */
.gallery-page .main-content {
  display: block !important;
  opacity: 1 !important;
  transform: translateY(0) !important;
}

.gallery-page .new-container {
  display: block !important;
  opacity: 1 !important;
  transform: none !important;
}

/* Animated noise effect for gallery page */
.gallery-page::before {
  content: "";
  position: fixed;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: transparent
    url("http://assets.iceable.com/img/noise-transparent.png") repeat 0 0;
  background-size: 300px 300px;
  animation: gallery-noise-animation 0.3s steps(5) infinite;
  opacity: 0.9;
  will-change: transform;
  z-index: 100;
  pointer-events: none;
}

@keyframes gallery-noise-animation {
  0% {
    transform: translate(0, 0);
  }
  10% {
    transform: translate(-2%, -3%);
  }
  20% {
    transform: translate(-4%, 2%);
  }
  30% {
    transform: translate(2%, -4%);
  }
  40% {
    transform: translate(-2%, 5%);
  }
  50% {
    transform: translate(-4%, 2%);
  }
  60% {
    transform: translate(3%, 0);
  }
  70% {
    transform: translate(0, 3%);
  }
  80% {
    transform: translate(-3%, 0);
  }
  90% {
    transform: translate(2%, 2%);
  }
  100% {
    transform: translate(1%, 0);
  }
}

/* Gallery specific link styling */
.gallery-page .gallery-link {
  position: relative;
  cursor: pointer;
  color: var(--gallery-color-text);
  padding: 0;
  display: inline-block;
  z-index: 1;
  text-decoration: none;
  font-size: var(--gallery-font-size-base);
  opacity: 1;
  transition: color var(--gallery-transition-medium);
  font-weight: 700;
}

.gallery-page .gallery-link::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: var(--gallery-color-text);
  z-index: -1;
  transition: width 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.gallery-page .gallery-link:hover::after {
  width: 100%;
}

.gallery-page .gallery-link:hover {
  color: black;
  mix-blend-mode: difference;
  opacity: 1;
}

/* Gallery paragraph styling */
.gallery-page .gallery-text {
  display: block;
  text-decoration: none;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: -0.01rem;
  -webkit-font-smoothing: antialiased;
}

/* Gallery Container */
.gallery-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  cursor: default; /* Default cursor for empty areas */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.gallery-container.dragging {
  cursor: grabbing !important;
}

.gallery-container.dragging * {
  cursor: grabbing !important;
  pointer-events: none !important;
}

.gallery-container.dragging .gallery-item {
  pointer-events: none !important;
}

/* Gallery Canvas - Optimized for performance */
.gallery-canvas {
  position: absolute;
  will-change: transform;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform: translate3d(0, 0, 0); /* Force hardware acceleration */
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Gallery Items - Performance optimized with drag cursor */
.gallery-item {
  position: absolute;
  overflow: hidden;
  background-color: #000000;
  cursor: grab; /* Clear indication that items are draggable */
  border-radius: var(--gallery-border-radius, 0px);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  will-change: transform;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  contain: layout style paint;
  transition: transform 0.2s ease; /* Smooth hover effect */
}

.gallery-item:hover {
  transform: translate3d(0, 0, 0) scale(1.02); /* Subtle hover feedback */
}

.gallery-item:active {
  cursor: grabbing;
}

.gallery-item-image-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.gallery-item-image-container::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  box-shadow: inset 0 0 var(--gallery-vignette-size, 0px) rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  pointer-events: none;
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  -webkit-touch-callout: none;
  will-change: transform;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
}

.gallery-item:hover img {
  transform: translate3d(0, 0, 0) scale(var(--gallery-hover-scale, 1.05));
}

.gallery-item-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 10px;
  z-index: 2;
}

/* Gallery Project Title */
.gallery-project-title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  text-align: center;
  pointer-events: none;
  z-index: 10002;
}

.gallery-project-title p {
  position: relative;
  height: 42px;
  color: #ffffff;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
  overflow: hidden;
}

.gallery-project-title p .word {
  position: relative;
  display: inline-block;
  font-family: "PP Neue Montreal", sans-serif;
  font-size: 36px;
  letter-spacing: -0.03em;
  text-transform: uppercase;
  transform: translateY(0%);
  will-change: transform;
}

/* Gallery Item Name and Number */
.gallery-item-name {
  font-family: "PP Neue Montreal", sans-serif;
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: -0.03em;
  margin-bottom: 2px;
  position: relative;
  overflow: hidden;
  height: 16px;
}

.gallery-item-name .word {
  position: relative;
  display: inline-block;
  will-change: transform;
}

.gallery-item-number {
  font-family: "TheGoodMonolith", monospace;
  color: #888888;
  font-size: 10px;
  font-weight: 400;
  position: relative;
  overflow: hidden;
  height: 14px;
}

.gallery-item-number .char {
  position: relative;
  display: inline-block;
  will-change: transform;
}

/* Gallery Expanded Item */
.gallery-expanded-item {
  position: fixed;
  z-index: 10000;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #000000;
  overflow: hidden;
  cursor: pointer;
  border-radius: var(--gallery-border-radius, 0px);
}

.gallery-expanded-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  pointer-events: none;
}

/* Gallery Overlay */
.gallery-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000000;
  pointer-events: none;
  opacity: 0;
  z-index: 9999;
}

.gallery-overlay.active {
  pointer-events: auto;
}

/* Gallery Page Vignette Effects */
.gallery-page-vignette-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9998;
}

.gallery-page-vignette {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: inset 0 0 var(--gallery-page-vignette-size, 0px)
    var(--gallery-page-vignette-color, rgba(0, 0, 0, 0.9));
}

.gallery-page-vignette-strong {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: inset 0 0 var(--gallery-page-vignette-strong-size, 0px)
    var(--gallery-page-vignette-strong-color, rgba(0, 0, 0, 0.95));
}

.gallery-page-vignette-extreme {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: inset 0 0 var(--gallery-page-vignette-extreme-size, 0px)
    var(--gallery-page-vignette-extreme-color, rgba(0, 0, 0, 1));
}

/* Gallery Caption Clone */
.gallery-caption-clone {
  position: fixed;
  z-index: 10002;
}

.gallery-caption-clone .gallery-item-name,
.gallery-caption-clone .gallery-item-number {
  overflow: hidden;
}

/* Gallery Instructions Overlay */
.gallery-instructions {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10003;
  opacity: 1;
  visibility: visible;
  transition: opacity 0.5s ease, visibility 0.5s ease;
  pointer-events: auto;
}

.gallery-instructions.hidden {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.gallery-instructions-content {
  text-align: center;
  color: #ffffff;
  max-width: 400px;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  animation: instructionsFadeIn 0.6s ease-out;
}

@keyframes instructionsFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.gallery-instructions-icon {
  margin-bottom: 1rem;
  animation: instructionsIconPulse 2s ease-in-out infinite;
}

@keyframes instructionsIconPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

.gallery-instructions-icon svg {
  color: #ffffff;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.gallery-instructions h3 {
  font-family: "PP Neue Montreal", sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.02em;
}

.gallery-instructions p {
  font-family: "PP Neue Montreal", sans-serif;
  font-size: 1rem;
  margin: 0 0 1rem 0;
  opacity: 0.9;
  line-height: 1.5;
}

.gallery-instructions p strong {
  color: #ffffff;
  font-weight: 600;
}

.gallery-instructions-hint {
  font-size: 0.875rem;
  opacity: 0.7;
  margin-top: 1rem;
}

.gallery-instructions-hint .mobile-hint,
.gallery-instructions-hint .desktop-hint {
  display: none;
}

/* Show appropriate hint based on device */
@media (max-width: 768px) {
  .gallery-instructions-hint .mobile-hint {
    display: inline;
  }
}

@media (min-width: 769px) {
  .gallery-instructions-hint .desktop-hint {
    display: inline;
  }
}

/* Tweakpane styles for gallery */
.gallery-page .tp-dfwv {
  z-index: 10000 !important;
}

.gallery-page .tp-rotv {
  opacity: 0.5 !important;
}

.gallery-page .tp-rotv.tp-rotv-expanded {
  opacity: 1 !important;
}

.gallery-page .tp-rotv.tp-rotv-collapsed {
  opacity: 0.7 !important;
}

/* ===== MOBILE RESPONSIVE STYLES ===== */

/* Mobile Landscape and Portrait (1024px and below) */
@media (max-width: 1024px) {
  /* Reduce noise animation intensity on tablets */
  .gallery-page::before {
    opacity: 0.6;
    animation-duration: 0.5s;
  }

  /* Adjust gallery project title for tablets */
  .gallery-project-title p .word {
    font-size: 28px;
  }

  /* Optimize gallery items for tablet touch */
  .gallery-item {
    border-radius: 8px;
  }

  .gallery-item:hover {
    transform: translate3d(0, 0, 0) scale(1.01);
  }

  /* Adjust vignette effects for tablets */
  .gallery-page-vignette {
    box-shadow: inset 0 0 150px rgba(0, 0, 0, 0.8);
  }
}

/* Mobile Portrait and Small Tablets (768px and below) */
@media (max-width: 768px) {
  /* Reduce noise effect for mobile performance */
  .gallery-page::before {
    opacity: 0.4;
    animation-duration: 0.8s;
    background-size: 200px 200px;
  }

  /* Mobile instructions styling */
  .gallery-instructions-content {
    max-width: 320px;
    padding: 1.5rem;
    margin: 0 1rem;
    border-radius: 16px;
  }

  .gallery-instructions h3 {
    font-size: 1.25rem;
  }

  .gallery-instructions p {
    font-size: 0.9rem;
  }

  .gallery-instructions-hint {
    font-size: 0.8rem;
  }

  /* Mobile-optimized gallery container */
  .gallery-container {
    cursor: default;
    touch-action: pan-x pan-y;
  }

  .gallery-container.dragging {
    cursor: default;
  }

  /* Mobile-optimized gallery items */
  .gallery-item {
    border-radius: 12px;
    cursor: default;
    transition: transform 0.3s ease;
  }

  .gallery-item:hover {
    transform: translate3d(0, 0, 0) scale(1);
  }

  .gallery-item:active {
    cursor: default;
    transform: translate3d(0, 0, 0) scale(0.98);
  }

  /* Mobile gallery project title */
  .gallery-project-title p .word {
    font-size: 24px;
    letter-spacing: -0.02em;
  }

  /* Mobile gallery item captions */
  .gallery-item-name {
    font-size: 11px;
    height: 14px;
  }

  .gallery-item-number {
    font-size: 9px;
    height: 12px;
  }

  /* Mobile vignette effects */
  .gallery-page-vignette {
    box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.9);
  }

  .gallery-page-vignette-strong {
    box-shadow: inset 0 0 80px rgba(0, 0, 0, 0.95);
  }

  /* Mobile expanded item adjustments */
  .gallery-expanded-item {
    border-radius: 12px;
    max-width: 90vw;
    max-height: 80vh;
  }
}

/* Small Mobile Devices (480px and below) */
@media (max-width: 480px) {
  /* Further reduce noise for small screens */
  .gallery-page::before {
    opacity: 0.3;
    animation-duration: 1s;
    background-size: 150px 150px;
  }

  /* Small mobile gallery project title */
  .gallery-project-title p .word {
    font-size: 20px;
    letter-spacing: -0.01em;
  }

  /* Small mobile gallery item captions */
  .gallery-item-name {
    font-size: 10px;
    height: 12px;
  }

  .gallery-item-number {
    font-size: 8px;
    height: 10px;
  }

  /* Small mobile vignette effects */
  .gallery-page-vignette {
    box-shadow: inset 0 0 60px rgba(0, 0, 0, 0.95);
  }

  /* Small mobile expanded item */
  .gallery-expanded-item {
    max-width: 95vw;
    max-height: 75vh;
    border-radius: 8px;
  }
}

/* Extra Small Mobile Devices (360px and below) */
@media (max-width: 360px) {
  /* Minimal noise for very small screens */
  .gallery-page::before {
    opacity: 0.2;
    animation: none;
  }

  /* Extra small mobile gallery project title */
  .gallery-project-title p .word {
    font-size: 18px;
  }

  /* Extra small mobile gallery item captions */
  .gallery-item-name {
    font-size: 9px;
    height: 11px;
  }

  .gallery-item-number {
    font-size: 7px;
    height: 9px;
  }

  /* Extra small mobile expanded item */
  .gallery-expanded-item {
    max-width: 98vw;
    max-height: 70vh;
  }
}
