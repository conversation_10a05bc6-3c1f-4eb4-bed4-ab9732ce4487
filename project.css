/* Project Page Styles - OH Architecture Inspired */

/* Project Hero Section with Parallax */
.project-hero {
  position: relative;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.hero-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.7);
  display: block;
  transform: none !important;
}

.project-hero-content {
  text-align: center;
  color: white;
  z-index: 2;
  max-width: 800px;
  padding: 0 2rem;
}

.project-breadcrumb {
  font-size: 1rem;
  margin-bottom: 2rem;
  opacity: 0.8;
}

.project-breadcrumb a {
  color: white;
  text-decoration: none;
  transition: opacity 0.3s ease;
}

.project-breadcrumb a:hover {
  opacity: 0.7;
}

.project-breadcrumb span {
  margin: 0 0.5rem;
}

.project-title {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 2rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.project-meta {
  display: flex;
  justify-content: center;
  gap: 2rem;
  font-size: 1.1rem;
  opacity: 0.9;
}

.project-meta span {
  position: relative;
}

.project-meta span:not(:last-child)::after {
  content: "•";
  position: absolute;
  right: -1rem;
  opacity: 0.6;
}

/* Project Description Section */
.project-description {
  padding: 8rem 0;
  background: #f8f8f8;
}

.project-description .container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 4rem;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 6rem;
  align-items: start;
}

.description-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #111;
  margin-bottom: 2rem;
  line-height: 1.2;
}

.description-content p {
  font-size: 1.2rem;
  line-height: 1.7;
  color: #666;
  margin-bottom: 1.5rem;
}

.project-details {
  background: white;
  padding: 3rem;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.detail-item {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.detail-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.detail-item h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1a3a2a;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item p {
  font-size: 1.1rem;
  color: #333;
  margin: 0;
}

/* Project Gallery */
.project-gallery {
  padding: 0;
}

.gallery-image {
  margin-bottom: 2rem;
  overflow: hidden;
}

.gallery-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.gallery-image:hover img {
  transform: scale(1.02);
  transition: transform 0.3s ease;
}

.full-width {
  height: 70vh;
}

.gallery-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.half-width {
  height: 50vh;
}

.gallery-row.thirds {
  grid-template-columns: 1fr 1fr 1fr;
}

.third-width {
  height: 40vh;
}

/* CTA Section */
.cta-section {
  padding: 8rem 4rem;
  text-align: center;
}

.cta-container {
  max-width: 1200px;
  margin: 0 auto;
}

.cta-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rem;
}

.cta-title {
  font-size: clamp(3rem, 6vw, 5rem);
  font-weight: 700;
  line-height: 1.1;
  letter-spacing: -0.02em;
  color: #000;
  text-align: center;
  max-width: 800px;
  font-size: 42px;
}

.cta-button {
  background: black;
  border: 2px solid #fff;
  color: #fff;
  padding: 1.2rem 3rem;
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 0.02em;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  border-radius: 0;
}

.cta-button span {
  position: relative;
  z-index: 1;
  display: block;
  overflow: hidden;
  height: 1.2em;
}

/* CTA button animation styles */
.cta-button .text-original,
.cta-button .text-hover {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.cta-button .text-hover {
  transform: translateY(100%);
}

/* Footer */
.footer {
  background: #111;
  color: white;
  padding: 6rem 0 2rem;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.footer-left {
  /* Left section styles */
}

.footer-logo {
  width: auto;
}

.footer-logo-image1 {
  height: 450px;
  width: 450px;
  margin-right: 1rem;
  margin-bottom: 3rem;
}

.footer-logo-image2 {
  width: 200px;
  filter: brightness(0) invert(1);
}

.footer-center {
  display: flex;
  margin-left: 5rem;
}

.footer-nav {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Footer navigation styles are inherited from main style.css */

.footer-right {
  /* Right section styles */
}

.footer-acknowledgement h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2a4d3b;
}

.footer-acknowledgement p {
  font-size: 0.95rem;
  opacity: 0.7;
  line-height: 1.5;
  margin-bottom: 2rem;
}

.footer-address h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2a4d3b;
}

.footer-address p {
  font-size: 1rem;
  opacity: 0.8;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.footer-bottom {
  padding-top: 2rem;
  border-top: 1px solid #333;
  text-align: center;
  margin-top: 4rem;
}

.footer-bottom p {
  opacity: 0.6;
  margin: 0;
  font-size: 0.9rem;
}

/* Container utility */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 4rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .project-description .container {
    grid-template-columns: 1fr;
    gap: 4rem;
    padding: 0 3rem;
  }

  .project-details {
    padding: 2rem;
  }

  .gallery-row {
    grid-template-columns: 1fr;
  }

  .half-width,
  .third-width {
    height: 50vh;
  }

  .footer-container {
    grid-template-columns: 1fr;
    gap: 3rem;
    padding: 0 3rem;
  }

  .footer-center {
    margin-left: 0;
  }

  .footer-logo-image1 {
    height: 300px;
    width: 300px;
  }

  .cta-section {
    padding: 6rem 3rem;
  }

  .cta-content {
    gap: 3rem;
  }

  .container {
    padding: 0 3rem;
  }
}

@media (max-width: 768px) {
  .project-hero-content {
    padding: 0 1.5rem;
  }

  .project-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .project-meta span::after {
    display: none;
  }

  .project-description {
    padding: 6rem 0;
  }

  .project-description .container {
    padding: 0 2rem;
  }

  .description-content h2 {
    font-size: 2rem;
  }

  .description-content p {
    font-size: 1.1rem;
  }

  .project-details {
    padding: 1.5rem;
  }

  .full-width {
    height: 50vh;
  }

  .half-width,
  .third-width {
    height: 40vh;
  }

  .cta-section {
    padding: 4rem 2rem;
  }

  .cta-title {
    font-size: 2.5rem;
  }

  .footer {
    padding: 4rem 0 2rem;
  }

  .footer-container {
    padding: 0 2rem;
    gap: 2rem;
  }

  .footer-logo-image1 {
    height: 200px;
    width: 200px;
  }

  .footer-nav {
    gap: 1rem;
  }

  .container {
    padding: 0 2rem;
  }
}

@media (max-width: 480px) {
  .project-hero-content {
    padding: 0 1rem;
  }

  .project-title {
    font-size: clamp(2.5rem, 8vw, 4rem);
  }

  .project-description .container,
  .container {
    padding: 0 1rem;
  }

  .full-width,
  .half-width,
  .third-width {
    height: 35vh;
  }

  .gallery-image {
    margin-bottom: 1rem;
  }

  .gallery-row {
    gap: 1rem;
  }

  .cta-section {
    padding: 3rem 1rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .footer-container {
    padding: 0 1rem;
  }

  .footer-logo-image1 {
    height: 150px;
    width: 150px;
  }

  .footer-logo-image2 {
    width: 150px;
  }

  .footer-acknowledgement h1 {
    font-size: 1rem;
  }

  .footer-acknowledgement p,
  .footer-address p {
    font-size: 0.9rem;
  }
}

/* Project Card Hover Effects for Works Page */
.project-card {
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.project-card:hover .project-image img {
  transform: scale(1.05);
}

.project-card .project-image {
  overflow: hidden;
}

.project-card .project-image img {
  transition: transform 0.5s ease;
}
